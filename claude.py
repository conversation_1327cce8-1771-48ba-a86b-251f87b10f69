import glob
import os
import re
import shutil
import sys
import traceback
from pathlib import Path

import anthropic

key = "************************************************************************************************************"


class ClaudeCodebaseAssistant:
    def __init__(self, api_key=key, project_root=None):
        self.client = anthropic.Anthropic(api_key=api_key or os.environ.get("ANTHROPIC_API_KEY"))
        self.project_root = Path(project_root or os.getcwd())
        self.file_index = {}
        self.index_codebase()

    def index_codebase(self, file_extensions=None):
        """Index the codebase to enable searching"""
        if file_extensions is None:
            file_extensions = ['.py', '.json', '.yaml', '.yml', '.md']

        print(f"Indexing codebase at {self.project_root}...")
        for ext in file_extensions:
            for filepath in glob.glob(f"{self.project_root}/**/*{ext}", recursive=True):
                filepath = Path(filepath)
                # Skip venv, __pycache__, etc.
                if any(part.startswith('.') or part == 'venv' or part == '__pycache__'
                       for part in filepath.parts):
                    continue

                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    rel_path = filepath.relative_to(self.project_root)
                    self.file_index[str(rel_path)] = {
                        'path': filepath,
                        'content': content,
                        # Extract class and function names for better searching
                        'classes': re.findall(r'class\s+(\w+)', content),
                        'functions': re.findall(r'def\s+(\w+)', content),
                    }
                except Exception as e:
                    print(f"❌ Error indexing {filepath}: {str(e)}", file=sys.stderr)
                    traceback.print_exc()

        python_files = sum(1 for f in self.file_index if f.endswith('.py'))
        print(f"Indexed {len(self.file_index)} files ({python_files} Python files)")

    def find_relevant_files(self, query):
        """Find files relevant to the query"""
        # Extract specific file mentions from the query
        file_mentions = set()
        for file in self.file_index.keys():
            filename = os.path.basename(file)
            if filename.lower() in query.lower():
                file_mentions.add(file)

        if file_mentions:
            return sorted(list(file_mentions))

        # Fall back to API to find relevant files
        file_list = "\n".join([
            f"- {path}: Contains classes {data['classes']} and functions {data['functions']}"
            for path, data in self.file_index.items()
        ])

        response = self.client.messages.create(
            model="claude-3-7-sonnet-20250219",
            max_tokens=1000,
            system="You are a code assistant. Your task is to identify the most relevant files for a given query.",
            messages=[
                {"role": "user",
                 "content": f"Based on this query: '{query}'\n\nHere are the files in the project:\n{file_list}\n\nList the most relevant file paths only, separated by commas, no explanation:"}
            ]
        )

        relevant_files = response.content[0].text.strip().split(',')
        relevant_files = [f.strip() for f in relevant_files]
        return [f for f in relevant_files if f in self.file_index]

    def locate_function_in_file(self, file_path, function_name):
        """Locate a specific function in a file and return its start/end lines"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        function_pattern = re.compile(rf'def\s+{function_name}\s*\(')
        start_line = -1
        indentation = ""

        # Find the function start
        for i, line in enumerate(lines):
            if function_pattern.search(line):
                start_line = i
                indentation = re.match(r'^(\s*)', line).group(1)
                break

        if start_line == -1:
            return None, None, None

        # Find the function end (first line with same or less indentation)
        end_line = len(lines)
        for i in range(start_line + 1, len(lines)):
            if lines[i].strip() and not lines[i].startswith(indentation + " "):
                if not (lines[i].strip().startswith("#") or lines[i].strip() == ""):
                    end_line = i
                    break

        return start_line, end_line, "".join(lines[start_line:end_line])

    def extract_class_definition(self, file_path, class_name):
        """Extract a class definition including all its methods"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        class_pattern = re.compile(rf'class\s+{class_name}\s*[:(]')
        start_line = -1
        indentation = ""

        # Find the class start
        for i, line in enumerate(lines):
            if class_pattern.search(line):
                start_line = i
                indentation = re.match(r'^(\s*)', line).group(1)
                break

        if start_line == -1:
            return None, None, None

        # Find the class end (first line with same or less indentation)
        end_line = len(lines)
        for i in range(start_line + 1, len(lines)):
            if lines[i].strip() and not lines[i].startswith(indentation + " "):
                if not (lines[i].strip().startswith("#") or lines[i].strip() == ""):
                    end_line = i
                    break

        return start_line, end_line, "".join(lines[start_line:end_line])

    def process_query(self, query):
        """Process a query about the codebase"""
        # Handle simple information queries
        if "how many" in query.lower() and "files" in query.lower():
            if "python" in query.lower() or ".py" in query.lower():
                count = sum(1 for f in self.file_index if f.endswith('.py'))
                return f"There are {count} Python files in this project."
            else:
                return f"There are {len(self.file_index)} files in this project."

        # Find relevant files
        relevant_files = self.find_relevant_files(query)
        if not relevant_files:
            return "I couldn't find relevant files for your query. Please specify a file to work with."

        # Parse the query to understand what to modify
        intent_response = self.client.messages.create(
            model="claude-3-7-sonnet-20250219",
            max_tokens=500,
            system="You analyze code-related queries to determine the intent.",
            messages=[
                {"role": "user",
                 "content": f"Query: {query}\n\nExtract the following information:\n1. What file needs to be modified?\n2. What function or class needs to be modified?\n3. What specific changes are needed?\n\nRespond in this format:\nFile: [filename]\nTarget: [function or class name]\nAction: [brief description of changes needed]"}
            ]
        )

        intent_text = intent_response.content[0].text

        # Parse the intent response
        file_match = re.search(r'File:\s*([\w./]+)', intent_text)
        target_match = re.search(r'Target:\s*([\w./]+)', intent_text)
        action_match = re.search(r'Action:\s*(.+?)(?=\n|$)', intent_text, re.DOTALL)

        if not (file_match and target_match and action_match):
            return "I couldn't clearly understand what needs to be modified. Please be more specific about which file and function to change."

        file_name = file_match.group(1).strip()
        target_name = target_match.group(1).strip()
        action = action_match.group(1).strip()

        # Find the actual file path from the index
        file_matches = [f for f in self.file_index.keys() if file_name.lower() in f.lower()]
        if not file_matches:
            return f"Could not find file {file_name} in the codebase."

        file_path = self.file_index[file_matches[0]]['path']

        # Handle different modification types
        return self.perform_targeted_edit(file_path, target_name, action, query)

    def perform_targeted_edit(self, file_path, target_name, action, query):
        """Perform a targeted edit on a specific function or class"""
        # Determine if target is a function or class
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check if target is a function or class
        is_function = bool(re.search(rf'def\s+{target_name}\s*\(', content))
        is_class = bool(re.search(rf'class\s+{target_name}\s*[:(]', content))

        if not (is_function or is_class):
            return f"Could not find function or class '{target_name}' in {file_path}."

        # Extract the target code
        if is_function:
            start_line, end_line, target_code = self.locate_function_in_file(file_path, target_name)
        else:
            start_line, end_line, target_code = self.extract_class_definition(file_path, target_name)

        if target_code is None:
            return f"Failed to extract code for {target_name} from {file_path}."

        # Create backup before making changes
        backup_path = str(file_path) + '.bak'
        shutil.copy2(file_path, backup_path)

        # Get the whole file content
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Add context around the target
        context_before = "".join(lines[max(0, start_line - 5):start_line])
        context_after = "".join(lines[end_line:min(len(lines), end_line + 5)])

        # Prepare request for Claude
        prompt = f"""I need to modify a specific part of this Python file. Here is the target code with some context:

Context before:
```python
{context_before}```

Target code to modify:
```python
{target_code}```

Context after:
```python
{context_after}```

Modification needed: {action}
Based on the query: {query}

Important requirements:
1. Provide ONLY the new version of the target code (function or class)
2. Preserve the indentation and coding style
3. Make minimal changes required to satisfy the request
4. Don't remove any existing functionality
5. Ensure the modified code will run without errors

Respond with ONLY the modified code for the target, no explanations or other text."""

        response = self.client.messages.create(
            model="claude-3-7-sonnet-20250219",
            max_tokens=3000,
            system="You are a code modification assistant. Your task is to carefully modify a specific section of code according to requirements.",
            messages=[{"role": "user", "content": prompt}]
        )

        # Extract the modified code
        modified_code = response.content[0].text.strip()

        # Clean up any code blocks
        if modified_code.startswith('```python'):
            modified_code = modified_code.split('```python', 1)[1]
        elif modified_code.startswith('```'):
            modified_code = modified_code.split('```', 1)[1]

        if modified_code.endswith('```'):
            modified_code = modified_code.rsplit('```', 1)[0]

        modified_code = modified_code.strip()

        # Perform safety checks
        if len(modified_code) < len(target_code) * 0.5:
            # Code is suspiciously short, restore backup
            shutil.copy2(backup_path, file_path)
            os.remove(backup_path)
            return f"Safety check failed: modified code is too short. No changes were made to the file."

        # Apply changes
        new_content = "".join(lines[:start_line]) + modified_code + "\n" + "".join(lines[end_line:])

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)

            # Get a clear explanation of the changes
            explain_response = self.client.messages.create(
                model="claude-3-7-sonnet-20250219",
                max_tokens=1000,
                system="You are a code reviewer explaining changes made to code.",
                messages=[
                    {"role": "user",
                     "content": f"Original code:\n```python\n{target_code}\n```\n\nModified code:\n```python\n{modified_code}\n```\n\nExplain the key changes that were made and why they satisfy this requirement: {action}"}
                ]
            )

            explanation = explain_response.content[0].text

            # Clean up backup on success
            os.remove(backup_path)

            return f"Successfully modified {os.path.basename(file_path)}\n\n{explanation}"

        except Exception as e:
            # Restore backup on error
            shutil.copy2(backup_path, file_path)
            os.remove(backup_path)
            return f"Error updating file: {str(e)}"


# Example usage
if __name__ == "__main__":
    import sys

    if len(sys.argv) < 2:
        print("Usage: python claude.py \"Your query here\"")
        sys.exit(1)

    project_root = os.getcwd()  # Or specify your project root path
    assistant = ClaudeCodebaseAssistant(project_root=project_root)
    query = sys.argv[1]

    print("Processing query:", query)
    result = assistant.process_query(query)
    print("\nResult:")
    print(result)
