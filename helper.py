import os
import sys
import traceback
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any

import pandas as pd
import pytz
from dash import html
from dateutil import parser

import trades_db
from models.strategy import Strategy
from models.time_frame import TimeFrame

ms_multiplier = 1000


def check_decimal(value):
    if value is None:
        return None

    return Decimal(value)


def store_decimal(value):
    if value is None:
        return None

    return str(value)


def calculate_duration_seconds(time_open, closed_date=None):
    """
    Calculate the duration between two datetime values in milliseconds.

    If closed_date is None, it defaults to current UTC time (for open trades).
    Handles timezone-aware and naive datetimes.
    """
    # Normalize timezones
    time_open = time_open.astimezone() if time_open.tzinfo else time_open
    closed_date = closed_date.astimezone() if closed_date and closed_date.tzinfo else closed_date

    # Default to now if closed_date is None
    if closed_date is None:
        closed_date = datetime.now(timezone.utc)

    # Calculate time difference
    time_diff = closed_date - time_open

    # Convert to seconds
    return time_diff.total_seconds()


def format_duration(seconds):
    # seconds = int(milliseconds)  # Convert ms to seconds
    days = int(seconds // 86400)
    hours = int((seconds % 86400) // 3600)  # Get remaining hours after full days
    minutes = int((seconds % 3600) // 60)
    # secs = seconds % 60

    # Format the output dynamically (h, m, s only if nonzero)
    parts = []
    if days:
        parts.append(f"{days}d")
    if hours:
        parts.append(f"{hours}h")
    if minutes:
        parts.append(f"{minutes}m")
    # if secs or not parts:  # Always show seconds if no hours/minutes exist
    #     parts.append(f"{secs}s")

    return " ".join(parts)


def format_decimal(param):
    return "0" if param is None else f"{param:.2f}"


# Custom converter function to handle empty fields
def decimal_converter(val):
    if val == '' or pd.isna(val):  # Check if the value is empty or NaN
        return Decimal(0)  # You can also use Decimal('0') or any other placeholder
    return Decimal(val)


def clean_decimal(value):
    """Removes $ sign and converts value to Decimal safely."""
    if isinstance(value, str):
        value = value.replace("$", "").strip()  # Remove $ and spaces
    return value


def convert_to_utc(dt):
    if dt.tzinfo is None:
        # Assume naive time is in local timezone
        local_tz = pytz.timezone("America/New_York")
        dt = local_tz.localize(dt)

    return dt.astimezone(pytz.UTC)


def dateStringToDate(date_string, date_format='%Y-%m-%d %H:%M:%S'):
    if "+00:00" in date_string:
        date_string = date_string.replace("+00:00", "")

    # Parse naive datetime (assumed UTC)
    utc_naive = datetime.strptime(date_string, date_format)
    utc_aware = pytz.utc.localize(utc_naive)

    # Convert to New York time
    new_york_tz = pytz.timezone("America/New_York")
    return utc_aware.astimezone(new_york_tz)


def dateStringToMS(date_str, date_format='%Y-%m-%d %H:%M:%S'):
    date_obj = dateStringToDate(date_str, date_format)
    return date_to_ms(date_obj)


def date_to_ms(date, multiplier=ms_multiplier):
    if date is None:
        return None
    return int(date.timestamp() * multiplier)


def mSToDate(date_in_ms, multiplier=ms_multiplier):
    if date_in_ms is None:
        return None

    # Create naive UTC datetime
    utc_naive = datetime.utcfromtimestamp(date_in_ms / multiplier)
    utc_aware = pytz.utc.localize(utc_naive)

    # Convert to local timezone
    local_tz = pytz.timezone("America/New_York")
    return utc_aware.astimezone(local_tz)


def get_now_date():
    # Get current time in UTC
    utc_now = datetime.utcnow()

    # Localize to UTC properly
    utc_aware = pytz.utc.localize(utc_now)

    # Convert to your local timezone
    local_tz = pytz.timezone("America/New_York")
    local_date = utc_aware.astimezone(local_tz)

    return local_date


def formatDate(date, date_format='%m/%d/%y %I:%M%p'):  # '%m-%d-%Y %H:%M'
    if date is None:
        return None
    return date.strftime(date_format)


# Custom converter for date fields
def date_converter(val):
    if val == '' or pd.isna(val):
        return None  # Or a default date if you prefer
    return datetime.strptime(val, '%Y-%m-%d %H:%M:%S.%f')  # Modify format as per your date format


def ms_to_utc_date(start_ms):
    return datetime.fromtimestamp(start_ms / ms_multiplier, tz=timezone.utc)


def get_duration_since_last_import(last_update):
    if last_update:
        now = datetime.now(timezone.utc)
        duration_ms = calculate_duration_seconds(last_update, now)
        duration_str = format_duration(duration_ms / 1000)  # Assuming this returns "3h 14m" etc.
        return duration_str
    return None


# Utility to split list into chunks of n
def chunk_list(lst, n):
    return [lst[i:i + n] for i in range(0, len(lst), n)]


def get_sierra_trades_file(path=None):
    """
    Retrieve the most recent Sierra Trades file from the specified path.
    Default is the user's desktop. Returns a DataFrame if the file is found, else None.
    """
    # Default to the user's desktop path if no path is provided
    if path is None:
        path = os.path.join(os.path.expanduser("~"), "Desktop")

    latest_file = None
    latest_date = None

    try:
        # Loop through files in the specified directory
        for filename in os.listdir(path):
            if filename.startswith("TradeActivityLogExport_") and filename.endswith(".csv"):
                date_str = filename.split("_")[1].replace(".csv", "")

                try:
                    # Parse the date
                    file_date = datetime.strptime(date_str, "%Y-%m-%d")

                    # Update if this date is the latest we've seen
                    if latest_date is None or file_date > latest_date:
                        latest_date = file_date
                        latest_file = filename

                except ValueError:
                    continue  # Skip files with an invalid date format

        # Load the most recent file into a DataFrame, if found
        if latest_file:
            file_path = os.path.join(path, latest_file)
            # Using converters to apply decimal_converter to multiple columns
            decimal_columns = ['Quantity', 'FilledQuantity', 'HighDuringPosition', 'LowDuringPosition',
                               'PositionQuantity', 'FillPrice', 'Price', 'Price2', 'Quantity']
            date_columns = ['TransDateTime', 'DateTime']
            # Merge dictionaries without using update
            converters = {**{col: decimal_converter for col in decimal_columns},
                          **{col: date_converter for col in date_columns}}

            df = pd.read_csv(file_path, sep='\t', converters=converters)
            print(f"Loaded file: {latest_file}")
            return df
        else:
            print("No matching file found.")
            return None
    except FileNotFoundError as e:
        print(f"❌ FileNotFoundError: The directory {path} does not exist. {str(e)}", file=sys.stderr)
        traceback.print_exc()
        return None


def infer_date_format(date_str: str) -> str:
    try:
        # Parse the date string into a datetime object
        dt = parser.parse(date_str)

        # Define potential format components
        formats = {
            "%Y": dt.year != None,
            "%m": dt.month != None,
            "%d": dt.day != None,
            "%H": dt.hour != None and dt.hour != 0,
            "%M": dt.minute != None and dt.minute != 0,
            "%S": dt.second != None and dt.second != 0,
            "%f": dt.microsecond != None and dt.microsecond != 0
        }

        # Map detected parts to their positions in the input string
        format_str = date_str
        format_str = format_str.replace(str(dt.year), "%Y", 1) if "%Y" in formats else format_str
        format_str = format_str.replace(f"{dt.month:02d}", "%m", 1) if "%m" in formats else format_str
        format_str = format_str.replace(f"{dt.day:02d}", "%d", 1) if "%d" in formats else format_str
        format_str = format_str.replace(f"{dt.hour:02d}", "%H", 1) if "%H" in formats else format_str
        format_str = format_str.replace(f"{dt.minute:02d}", "%M", 1) if "%M" in formats else format_str
        format_str = format_str.replace(f"{dt.second:02d}", "%S", 1) if "%S" in formats else format_str
        format_str = format_str.replace(f"{dt.microsecond:06d}", "%f", 1) if "%f" in formats else format_str

        return format_str
    except Exception as e:
        print(f"❌ Error in infer_date_format: {str(e)}", file=sys.stderr)
        traceback.print_exc()
        return f"Error: {e}"


def get_export_data() -> list[Any]:
    export_data = []
    # Get all strategies from the database
    strategies = trades_db.TradesDB.getStrategiesList()

    # Add strategies to export data
    for strategy in strategies:
        export_data.append({
            "data_type": "STRATEGY",
            "id": strategy.strat_id,
            "name": strategy.name,
            "display_name": strategy.display_name,
            "description": strategy.description,
            "notes": strategy.notes,
            "images": strategy.images,
            "sort_order": strategy.sort_order,
            "is_active": strategy.is_active,
            "createdDate": date_to_ms(strategy.created_date) if strategy.created_date else "",
            "modifiedDate": date_to_ms(strategy.modified_date) if strategy.modified_date else "",
            "username": strategy.username,
            # Trade-specific fields (empty for strategies)
            "exchange_trade_id": "",
            "symbol": "",
            "status": "",
            "direction": "",
            "exchange": "",
            "created_date": "",
            "closed_date": "",
            "strategy": "",
            "time_frame": "",
            "profit": ""
        })

    # Get all time frames from the database
    time_frames = trades_db.TradesDB.getTimeFrameList()

    # Add time frames to export data
    for tf in time_frames:
        export_data.append({
            "data_type": "TIMEFRAME",
            "id": f"tf_{tf.time_frame_id}",
            "name": tf.name,
            "display_name": tf.display_name,
            "description": tf.description,
            "notes": tf.notes,
            "images": tf.images,
            "sort_order": tf.sort_order,
            "is_active": tf.is_active,
            "createdDate": date_to_ms(tf.created_date) if tf.created_date else "",
            "modifiedDate": date_to_ms(tf.modified_date) if tf.modified_date else "",
            "username": tf.username,
            # Trade-specific fields (empty for timeframes)
            "exchange_trade_id": "",
            "symbol": "",
            "status": "",
            "direction": "",
            "exchange": "",
            "created_date": "",
            "closed_date": "",
            "strategy": "",
            "time_frame": "",
            "profit": ""
        })

    # Get all trades from the database
    trades = trades_db.TradesDB.get_trades()

    def get_strategy_names_csv(strategy_ids_csv):
        """Convert comma-separated strategy IDs to comma-separated strategy names."""
        if not strategy_ids_csv:
            return ""

        strategy_ids = [s.strip() for s in strategy_ids_csv.split(',') if s.strip()]
        strategy_names = []

        for strat_id in strategy_ids:
            strat = trades_db.TradesDB.getStrategyById(int(strat_id))
            if strat:
                strategy_names.append(strat.name)

        return ','.join(strategy_names)

    # Add trades to export data (only if they have strategy, notes, or time_frame)
    trade_count = 0
    for trade in trades:
        if trade.strategy or trade.notes or trade.time_frame:
            export_data.append({
                "data_type": "TRADE",
                "exchange_trade_id": trade.exchange_trade_id,
                "symbol": trade.symbol,
                "status": trade.status.name if trade.status else "",
                "direction": trade.direction.name if trade.direction else "",
                "exchange": trade.exchange.name if trade.exchange else "",
                "created_date": date_to_ms(trade.created_date) if trade.created_date else "",
                "closed_date": date_to_ms(trade.closed_date) if trade.closed_date else "",
                "strategy": get_strategy_names_csv(trade.strategy) if trade.strategy else "",
                "notes": trade.notes if trade.notes else "",
                "time_frame": trade.time_frame if trade.time_frame else "",
                "profit": str(trade.profit) if trade.profit is not None else "",
                # Strategy/timeframe-specific fields (empty for trades)
                "id": "",
                "name": "",
                "display_name": "",
                "description": "",
                "images": "",
                "sort_order": "",
                "is_active": "",
                "createdDate": "",
                "modifiedDate": "",
                "username": ""
            })
            trade_count += 1

    return export_data


def process_combined_file(df):
    """Process a combined CSV file containing strategies, timeframes, and trades."""
    strategy_updated = 0
    strategy_created = 0
    timeframe_updated = 0
    timeframe_created = 0
    trade_updated = 0

    # Separate data by type
    strategies_df = df[df['data_type'] == 'STRATEGY']
    timeframes_df = df[df['data_type'] == 'TIMEFRAME']
    trades_df = df[df['data_type'] == 'TRADE']

    # Process strategies first
    for _, row in strategies_df.iterrows():
        strat = Strategy.fromRow(row)
        cursor = trades_db.get_db_cursor()
        cursor.execute("SELECT id FROM Strategy WHERE name = ?", (strat.name,))
        existing = cursor.fetchone()

        if existing:
            # Update existing strategy
            cursor.execute(
                """UPDATE Strategy SET name = ?, description = ?, notes = ?, images = ?, createdDate = ?, modifiedDate = ?, username = ?
                 WHERE id = ?""",
                (strat.name,
                 strat.description,
                 strat.notes,
                 strat.images,
                 date_to_ms(strat.created_date) if strat.created_date else None,
                 date_to_ms(strat.modified_date) if strat.modified_date else date_to_ms(get_now_date()),
                 strat.username,
                 existing[0]))
            strategy_updated += 1
        else:
            # Create a new strategy
            cursor.execute(
                """INSERT INTO Strategy
                    (name, display_name, description, notes, images, sort_order, is_active, createdDate, modifiedDate, username)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (strat.name, strat.display_name, strat.description, strat.notes, strat.images, strat.sort_order or 100,
                 1 if strat.is_active else 0,
                 date_to_ms(strat.created_date) if strat.created_date else date_to_ms(get_now_date()),
                 date_to_ms(get_now_date()),
                 strat.username))
            strategy_created += 1

        trades_db.get_db_connection().commit()

    # Process timeframes second
    for _, row in timeframes_df.iterrows():
        tf = TimeFrame.fromRow(row)
        cursor = trades_db.get_db_cursor()
        cursor.execute("SELECT id FROM TimeFrame WHERE name = ?", (tf.name,))
        existing = cursor.fetchone()

        if existing:
            # Update existing time frame
            cursor.execute(
                """UPDATE TimeFrame SET name = ?, display_name = ?, description = ?, notes = ?, images = ?,
                   sort_order = ?, is_active = ?, createdDate = ?, modifiedDate = ?, username = ?
                   WHERE id = ?""",
                (tf.name, tf.display_name, tf.description, tf.notes, tf.images, tf.sort_order,
                 1 if tf.is_active else 0,
                 date_to_ms(tf.created_date) if tf.created_date else None,
                 date_to_ms(tf.modified_date) if tf.modified_date else date_to_ms(get_now_date()),
                 tf.username,
                 existing[0]))
            timeframe_updated += 1
        else:
            # Create a new time frame
            cursor.execute(
                """INSERT INTO TimeFrame
                   (name, display_name, description, notes, images, sort_order, is_active, createdDate, modifiedDate, username)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (tf.name, tf.display_name, tf.description, tf.notes, tf.images, tf.sort_order or 100,
                 1 if tf.is_active else 0,
                 date_to_ms(tf.created_date) if tf.created_date else date_to_ms(get_now_date()),
                 date_to_ms(get_now_date()),
                 tf.username))
            timeframe_created += 1

        trades_db.get_db_connection().commit()

    # Process trades last
    trade_updated = process_trades_data(trades_df)

    # Build result message
    result_messages = []
    if strategy_created > 0 or strategy_updated > 0:
        result_messages.append(f"Strategies: {strategy_created} created, {strategy_updated} updated")

    if timeframe_created > 0 or timeframe_updated > 0:
        result_messages.append(f"Time frames: {timeframe_created} created, {timeframe_updated} updated")

    if trade_updated > 0:
        result_messages.append(f"Trades: {trade_updated} updated")

    if not result_messages:
        return html.Div([
            html.I(className="bi bi-info-circle-fill text-info me-2"),
            "No changes were made."
        ])

    return html.Div([
        html.I(className="bi bi-check-circle-fill text-success me-2"),
        f"Successfully processed: {' • '.join(result_messages)}"
    ])


def process_trades_data(df):
    """Process trades data from a DataFrame and return count of updated trades."""
    updated_count = 0

    for _, row in df.iterrows():
        exchange_trade_id = row["exchange_trade_id"]

        # Process strategy field
        strategy = row.get("strategy") if "strategy" in df.columns and not pd.isna(row.get("strategy")) else None
        if strategy and not isinstance(strategy, str):
            strategy = str(strategy)
        if strategy:
            strategy = [s.strip() for s in strategy.split(",") if s.strip()]

        # Process time_frame field
        time_frame = row.get("time_frame") if "time_frame" in df.columns and not pd.isna(
            row.get("time_frame")) else None
        if time_frame and not isinstance(time_frame, str):
            time_frame = str(time_frame)
        if time_frame:
            time_frame = [tf.strip() for tf in time_frame.split(",") if tf.strip()]

        notes = row.get("notes") if "notes" in df.columns and not pd.isna(row.get("notes")) else None

        # Update strategy
        if strategy:
            updated = trades_db.TradesDB.save_trade_strategy(strategy, exchange_trade_id=exchange_trade_id)
            if updated:
                updated_count += 1

        # Update time frame
        if time_frame:
            updated = trades_db.TradesDB.save_trade_time_frame(time_frame, exchange_trade_id=exchange_trade_id)
            if updated:
                updated_count += 1

        # Update notes
        if notes:
            updated = trades_db.TradesDB.save_trade_notes(notes, exchange_trade_id=exchange_trade_id)
            if updated:
                updated_count += 1

    return updated_count
