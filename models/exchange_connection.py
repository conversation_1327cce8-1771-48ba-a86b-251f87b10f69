"""
Exchange Connection Model

Represents an exchange API connection for a user, including credentials and connection status.
"""

import time
from typing import Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum


class ConnectionStatus(Enum):
    """Exchange connection status types"""
    PENDING = "pending"
    CONNECTED = "connected"
    ERROR = "error"
    DISABLED = "disabled"


class SupportedExchange(Enum):
    """Supported exchange types"""
    BYBIT = "bybit"
    BINANCE = "binance"
    COINBASE = "coinbase"
    BITGET = "bitget"
    HYPERLIQUID = "hyperliquid"
    BLOFIN = "blofin"
    WOOX = "woox"
    PHEMEX = "phemex"
    DERIBIT = "deribit"


@dataclass
class ExchangeConnection:
    """
    Represents an exchange API connection for a user.
    
    Attributes:
        id: Database primary key
        user_id: Foreign key to Users table
        exchange_name: Name of the exchange (e.g., 'bybit', 'binance')
        account_name: User-defined label for this connection
        api_key: Exchange API key (stored in plain text)
        api_secret_encrypted: Encrypted API secret
        is_testnet: Whether this connection uses testnet/sandbox environment
        is_active: Whether this connection is active
        connection_status: Current connection status
        last_sync_date: Timestamp of last successful sync
        created_date: Timestamp when connection was created
        modified_date: Timestamp when connection was last modified
    """

    id: Optional[int] = None
    user_id: int = None
    exchange_name: str = None
    account_name: str = None
    api_key: str = None
    api_secret_encrypted: str = None
    is_testnet: bool = False
    is_active: bool = True
    connection_status: str = ConnectionStatus.PENDING.value
    last_sync_date: Optional[int] = None
    created_date: Optional[int] = None
    modified_date: Optional[int] = None
    paper_trade: Optional[bool] = False

    def __post_init__(self):
        """Set timestamps if not provided"""
        current_time = int(time.time() * 1000)
        if self.created_date is None:
            self.created_date = current_time
        if self.modified_date is None:
            self.modified_date = current_time

    @classmethod
    def create_new(cls, user_id: int, exchange_name: str, account_name: str,
                   api_key: str, api_secret_encrypted: str, is_testnet: bool = False, paper_trade: bool = False
                   ) -> 'ExchangeConnection':
        """
        Create a new exchange connection instance.

        Args:
            user_id: ID of the user who owns this connection
            exchange_name: Name of the exchange
            account_name: User-defined label for this connection
            api_key: Exchange API key
            api_secret_encrypted: Encrypted API secret
            is_testnet: Whether to use testnet/sandbox environment
            paper_trade: Whether to mock order execution

        Returns:
            ExchangeConnection: New instance with timestamps set
        """
        current_time = int(time.time() * 1000)

        return cls(
            user_id=user_id,
            exchange_name=exchange_name.lower(),
            account_name=account_name,
            api_key=api_key,
            api_secret_encrypted=api_secret_encrypted,
            is_testnet=is_testnet,
            is_active=True,
            connection_status=ConnectionStatus.CONNECTED.value,
            created_date=current_time,
            modified_date=current_time,
            paper_trade=paper_trade
        )

    def update_status(self, status: ConnectionStatus, sync_date: Optional[int] = None):
        """
        Update the connection status and optionally the last sync date.

        Args:
            status: New connection status
            sync_date: Optional timestamp of last sync
        """
        self.connection_status = status.value
        self.modified_date = int(time.time() * 1000)

        if sync_date is not None:
            self.last_sync_date = sync_date
        elif status == ConnectionStatus.CONNECTED:
            self.last_sync_date = self.modified_date

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the exchange connection to a dictionary.

        Returns:
            Dict containing all exchange connection data
        """
        return {
            "id": self.id,
            "user_id": self.user_id,
            "exchange_name": self.exchange_name,
            "account_name": self.account_name,
            "api_key": self.api_key,
            "api_secret_encrypted": self.api_secret_encrypted,
            "is_testnet": self.is_testnet,
            "is_active": self.is_active,
            "connection_status": self.connection_status,
            "last_sync_date": self.last_sync_date,
            "created_date": self.created_date,
            "modified_date": self.modified_date,
            "paper_trade": self.paper_trade
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExchangeConnection':
        """
        Create an ExchangeConnection instance from a dictionary.

        Args:
            data: Dictionary containing exchange connection data

        Returns:
            ExchangeConnection: Instance created from the data
        """
        return cls(
            id=data.get("id"),
            user_id=data.get("user_id"),
            exchange_name=data.get("exchange_name"),
            account_name=data.get("account_name"),
            api_key=data.get("api_key"),
            api_secret_encrypted=data.get("api_secret_encrypted"),
            is_testnet=bool(data.get("is_testnet", False)),
            is_active=bool(data.get("is_active", True)),
            connection_status=data.get("connection_status", ConnectionStatus.PENDING.value),
            last_sync_date=data.get("last_sync_date"),
            created_date=data.get("created_date"),
            modified_date=data.get("modified_date"),
            paper_trade=data.get("paper_trade", False)
        )

    def get_display_status(self) -> str:
        """
        Get a user-friendly display status.

        Returns:
            String representation of the connection status
        """
        status_map = {
            ConnectionStatus.PENDING.value: "⏳ Pending",
            ConnectionStatus.CONNECTED.value: "✅ Connected",
            ConnectionStatus.ERROR.value: "❌ Error",
            ConnectionStatus.DISABLED.value: "⏸️ Disabled"
        }
        return status_map.get(self.connection_status, "❓ Unknown")

    def is_valid_exchange(self) -> bool:
        """
        Check if the exchange name is supported.

        Returns:
            True if exchange is supported, False otherwise
        """
        try:
            SupportedExchange(self.exchange_name.lower())
            return True
        except ValueError:
            return False

    def __str__(self) -> str:
        """String representation of the exchange connection"""
        return (f"ExchangeConnection(id={self.id}, "
                f"user_id={self.user_id}, "
                f"exchange={self.exchange_name}, "
                f"account={self.account_name}, "
                f"status={self.connection_status}), "
                f"paper_trade={self.paper_trade}")

    def __repr__(self) -> str:
        """Detailed string representation"""
        return self.__str__()
