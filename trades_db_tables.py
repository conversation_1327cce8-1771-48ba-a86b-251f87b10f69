import sqlite3
import time

import trades_db


class TradeDB_tables:

    @staticmethod
    def signup_user(username, password_hash):
        cursor = trades_db.get_db_cursor()
        cursor.execute("INSERT INTO Users (username, password_hash) VALUES (?, ?)", (username, password_hash))
        trades_db.get_db_connection().commit()

    @staticmethod
    def login_user(username):
        cursor = trades_db.get_db_cursor()
        cursor.execute("SELECT id, password_hash FROM Users WHERE username = ?", (username,))
        row = cursor.fetchone()

        if row:
            return row
        return None

    @staticmethod
    def create_db_and_tables():
        """Creates the DB file and required tables without calling get_db_cursor()."""
        print(f"🔧 Starting create_db_and_tables() - DB_PATH: {trades_db.DB_PATH}")

        connection = sqlite3.connect(trades_db.DB_PATH)
        cursor = connection.cursor()

        print("📊 Creating Trade table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Trade (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchange_trade_id STRING,
                status STRING,
                tradeQty TEXT,
                openQty TEXT,
                timeOpen INTEGER,
                lastUpdate INTEGER,
                timeClose INTEGER,
                duration INTEGER,
                direction TEXT,
                symbol STRING,
                chartLink TEXT,
                notes TEXT,
                notional TEXT,
                leverage STRING,
                avgOpenPrice TEXT,
                avgClosePrice TEXT,
                riskPercent TEXT,
                accountBalance TEXT,
                riskAmt TEXT,
                profit TEXT,
                fees TEXT,
                strategy STRING,
                time_frame STRING,
                username TEXT,
                source TEXT,
                paper_trade BOOLEAN,
                exchange_connection_id INTEGER,
                FOREIGN KEY (exchange_connection_id) REFERENCES ExchangeConnection(id) ON DELETE SET NULL
            )
            ''')
        print("✅ Trade table created")
        print("🔗 Creating Trade table index...")
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_trade_exchange_connection_id
            ON Trade(exchange_connection_id);
            ''')
        print("✅ Trade table index created")
        print("🔗 Creating Orders table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id STRING,
                created_date INTEGER,
                filled_date INTEGER,
                symbol STRING,
                orderType STRING,
                orderStatus STRING,
                buySell STRING,
                reduce INTEGER,
                price TEXT,
                fillPrice TEXT,
                fee TEXT,
                quantity TEXT,
                filledQuantity TEXT,
                trade_id INT REFERENCES Trade(id) ON DELETE CASCADE
            )
            ''')
        print("✅ Orders table created")
        print("🔗 Creating SierraActivity table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS SierraActivity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                activityId INTEGER,
                activityType STRING,
                date INTEGER,
                transDate INTEGER,
                symbol STRING,
                orderActionSource STRING,
                quantity TEXT,
                orderType STRING,
                buySell STRING,
                price TEXT,
                price2 TEXT,
                internalOrderId STRING,
                serviceOrderId STRING,
                orderStatus STRING,
                exchangeOrderId STRING,
                fillPrice TEXT,
                filledQuantity TEXT,
                tradeAccount STRING,
                openClose STRING,
                parentInternalOrderId STRING,
                positionQuantity TEXT,
                fillExecutionServiceId STRING,
                highDuringPosition TEXT,
                lowDuringPosition TEXT,
                note TEXT,
                accountBalance TEXT,
                clientOrderId STRING,
                timeInForce STRING,
                isAutomated STRING,
                order_id INT REFERENCES Orders(id) ON DELETE CASCADE
            )
            ''')
        print("✅ SierraActivity table created")
        print("🔗 Creating BybitOrder table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS BybitOrder (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bybit_order_id INTEGER NOT NULL,
                orderLinkId STRING,
                blockTradeId STRING,
                symbol STRING,
                price TEXT,
                qty TEXT,
                side STRING,
                isLeverage STRING,
                positionIdx TEXT,
                orderStatus STRING,
                createType STRING,
                cancelType STRING,
                rejectReason STRING,
                avgPrice TEXT,
                leavesQty TEXT,
                leavesValue TEXT,
                cumExecQty TEXT,
                cumExecValue TEXT,
                cumExecFee TEXT,
                timeInForce STRING,
                orderType STRING,
                stopOrderType STRING,
                orderIv STRING,
                triggerPrice TEXT,
                takeProfit TEXT,
                stopLoss TEXT,
                tpslMode STRING,
                tpLimitPrice TEXT,
                slLimitPrice TEXT,
                tpTriggerBy STRING,
                slTriggerBy STRING,
                triggerDirection INTEGER,
                triggerBy STRING,
                lastPriceOnCreated STRING,
                reduceOnly BOOLEAN,
                closeOnTrigger BOOLEAN,
                placeType STRING,
                smpType STRING,
                smpGroup INTEGER,
                smpOrderId STRING,
                createdTime STRING,
                updatedTime STRING,
                order_id INT REFERENCES Orders(id) ON DELETE CASCADE
            )
            ''')
        print("✅ BybitOrder table created")
        print("🔗 Creating CoinbaseFill table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS CoinbaseFill (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                commission TEXT,
                liquidity_indicator TEXT,
                coinbase_order_id TEXT,
                price TEXT,
                product_id TEXT,
                retail_portfolio_id TEXT,
                sequence_timestamp INTEGER,
                side TEXT,
                size TEXT,
                size_in_quote INTEGER,
                trade_id TEXT,
                entry_id TEXT,
                trade_time INTEGER,
                trade_type TEXT,
                user_id TEXT,
                order_id INT REFERENCES Orders(id) ON DELETE CASCADE
            )
            ''')
        print("✅ CoinbaseFill table created")
        print("🔗 Creating CoinbaseOrder table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS CoinbaseOrder (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                coinbase_order_id TEXT,
                attached_order_configuration TEXT,
                attached_order_id TEXT,
                average_filled_price TEXT,
                cancel_message TEXT,
                client_order_id TEXT,
                completion_percentage TEXT,
                created_time INTEGER,
                edit_history TEXT,
                fee TEXT,
                filled_size TEXT,
                filled_value TEXT,
                is_liquidation INTEGER,
                last_fill_time INTEGER,
                leverage TEXT,
                margin_type TEXT,
                number_of_fills TEXT,
                order_configuration TEXT,
                order_placement_source TEXT,
                order_type TEXT,
                originating_order_id TEXT,
                outstanding_hold_amount TEXT,
                pending_cancel INTEGER,
                product_id TEXT,
                product_type TEXT,
                reject_message TEXT,
                reject_reason TEXT,
                retail_portfolio_id TEXT,
                settled INTEGER,
                side TEXT,
                size_in_quote INTEGER,
                size_inclusive_of_fees INTEGER,
                status TEXT,
                time_in_force TEXT,
                total_fees TEXT,
                total_value_after_fees TEXT,
                trigger_status TEXT,
                user_id TEXT,
                order_id INT REFERENCES Orders(id) ON DELETE CASCADE
            )
            ''')
        print("✅ CoinbaseOrder table created")
        print("🔗 Creating Strategy table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Strategy (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                display_name TEXT NOT NULL,
                description TEXT,
                notes TEXT,
                images TEXT,
                sort_order INTEGER,
                is_active BOOLEAN DEFAULT 1,
                createdDate INTEGER,
                modifiedDate INTEGER,
                username TEXT
            )
            ''')
        print("✅ Strategy table created")
        print("🔗 Creating TimeFrame table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS TimeFrame (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                display_name TEXT NOT NULL,
                description TEXT,
                notes TEXT,
                images TEXT,
                sort_order INTEGER,
                is_active BOOLEAN DEFAULT 1,
                createdDate INTEGER,
                modifiedDate INTEGER,
                username TEXT
            )
            ''')
        print("✅ TimeFrame table created")
        print("🔗 Creating default time frames...")
        # Insert default time frames if table is empty
        cursor.execute("SELECT COUNT(*) FROM TimeFrame")
        print("   📊 Checking for existing time frames...")
        count = cursor.fetchone()[0]
        print(f"   📊 Found {count} time frames")
        if count == 0:
            time_frames = [
                ("1m", "1 Minute", "One minute chart", 10),
                ("3m", "3 Minutes", "Three minute chart", 15),
                ("5m", "5 Minutes", "Five minute chart", 20),
                ("10m", "10 Minutes", "Ten minute chart", 25),
                ("15m", "15 Minutes", "Fifteen minute chart", 30),
                ("30m", "30 Minutes", "Thirty minute chart", 40),
                ("1h", "1 Hour", "One hour chart", 50),
                ("2h", "2 Hours", "Two hour chart", 55),
                ("4h", "4 Hours", "Four hour chart", 60),
                ("6h", "6 Hours", "Six hour chart", 65),
                ("12h", "12 Hours", "Twelve hour chart", 70),
                ("1d", "1 Day", "Daily chart", 80),
                ("1w", "1 Week", "Weekly chart", 90)
            ]
            print("   📊 Inserting default time frames...")
            for tf in time_frames:
                print(f"   📊 Inserting {tf[0]}...")
                cursor.execute(
                    "INSERT INTO TimeFrame (name, display_name, description, sort_order, createdDate, modifiedDate) VALUES (?, ?, ?, ?, ?, ?)",
                    (tf[0], tf[1], tf[2], tf[3], int(time.time() * 1000), int(time.time() * 1000))
                )
                print(f"   📊 Inserted {tf[0]}")
        print("✅ Default time frames created")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS _strat_trade_relations (
                tradeReference INTEGER, 
                stratReference INTEGER
            )
            ''')
        print("✅ _strat_trade_relations table created")
        print("👤 Creating Users table...")
        cursor.execute('''
            CREATE TABLE Users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT,
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            ''')
        print("✅ Users table created")

        print("🔗 Creating ExchangeConnection table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ExchangeConnection (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                exchange_name TEXT NOT NULL,
                account_name TEXT NOT NULL,
                api_key TEXT NOT NULL,
                api_secret_encrypted TEXT NOT NULL,
                is_testnet BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                connection_status TEXT DEFAULT 'pending',
                last_sync_date INTEGER,
                created_date INTEGER NOT NULL,
                modified_date INTEGER NOT NULL,
                paper_trade BOOLEAN DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
                UNIQUE(user_id, exchange_name, account_name)
            );
            ''')
        print("✅ ExchangeConnection table created")
        print("🔗 Creating ExchangeConnection indexes...")
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_exchange_connection_user_id
            ON ExchangeConnection(user_id);
            ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_exchange_connection_exchange
            ON ExchangeConnection(exchange_name);
            ''')
        print("✅ ExchangeConnection table created")

        print("💾 Committing changes...")
        connection.commit()
        print("✅ Database changes committed")

        # Verify Users table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='Users'")
        users_table = cursor.fetchone()
        print(f"🔍 Users table verification: {users_table}")

        connection.close()
        print("🔧 create_db_and_tables() completed")

