"""
Paper Trade Simulator

This module provides functionality to simulate paper trade fills by checking current market data
against order prices to determine if orders would have been filled.
"""
import traceback
from decimal import Decimal
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)


class PaperTradeSimulator:
    """Simulates paper trade fills using market data"""

    def __init__(self, candle_service=None):
        if candle_service is None:
            from services.candle_service import CandleService
            self.candle_service = CandleService()
        else:
            self.candle_service = candle_service

    def simulate_order_fills(self, trade) -> Dict[str, Dict]:
        """
        Simulate order fills for a paper trade using comprehensive historical market data analysis.

        This method processes market data chronologically from order creation to present time,
        simulating realistic order fills based on actual price movements.

        Args:
            trade: Trade object with paper_trade=True

        Returns:
            dict: Dictionary mapping order_id to fill simulation results
                 Format: {order_id: {"status": str, "filled": bool, "fill_price": Decimal, "reason": str, "fill_time": datetime}}
        """
        if not trade.paper_trade:
            raise ValueError("This function is only for paper trades")

        # Import here to avoid circular imports
        import helper
        import time
        from datetime import datetime, timezone

        results = {}

        # Validate trade has orders
        if not trade.trade_orders:
            logger.warning(f"Trade {trade.id_field} has no orders to simulate")
            return results

        try:
            # Step 1: Calculate historical data range
            earliest_created_date = None
            for order in trade.trade_orders:
                if order.created_date:
                    if earliest_created_date is None or order.created_date < earliest_created_date:
                        earliest_created_date = order.created_date

            if earliest_created_date is None:
                logger.error(f"No valid created_date found for any orders in trade {trade.id_field}")
                for order in trade.trade_orders:
                    results[order.order_id] = {
                        "status": "Unknown",
                        "filled": False,
                        "fill_price": Decimal("0"),
                        "reason": "No valid order creation date found",
                        "fill_time": None
                    }
                return results

            # Set simulation time range
            start_ms = helper.date_to_ms(earliest_created_date)
            end_ms = helper.date_to_ms(helper.get_now_date())
            exchange_name = trade.exchange_connection.exchange_name.lower()

            print(f"Starting historical simulation for {trade.symbol} from {earliest_created_date} to present")
            print(f"Time range: {start_ms} to {end_ms} ({(end_ms - start_ms) / (1000 * 60 * 60 * 24):.1f} days)")

            # Initialize order tracking
            unfilled_orders = []
            for order in trade.trade_orders:
                # Use string comparison to avoid circular imports
                order_status_str = str(order.orderStatus).lower() if hasattr(order.orderStatus, 'value') else str(
                    order.orderStatus).lower()

                if "filled" not in order_status_str:
                    unfilled_orders.append(order)
                    results[order.order_id] = {
                        "status": "New",
                        "filled": False,
                        "fill_price": Decimal("0"),
                        "reason": "Order pending simulation",
                        "fill_time": None
                    }
                else:
                    # Order already filled, preserve existing status
                    results[order.order_id] = {
                        "status": "Filled",
                        "filled": True,
                        "fill_price": order.fillPrice if order.fillPrice else order.price,
                        "reason": "Order already filled before simulation",
                        "fill_time": order.filled_date
                    }

            if not unfilled_orders:
                print(f"All orders in trade {trade.id_field} are already filled")
                return results

            # Step 2: Process historical data in chronological batches
            current_start_ms = start_ms
            batch_count = 0
            total_candles_processed = 0

            while current_start_ms < end_ms and unfilled_orders:
                batch_count += 1

                # Calculate batch end time (500 minutes = 500 1-minute candles)
                batch_end_ms = min(current_start_ms + (500 * 60 * 1000), end_ms)

                print(
                    f"\nProcessing batch {batch_count}: {helper.mSToDate(current_start_ms)} to {helper.mSToDate(batch_end_ms)}")

                try:
                    # Fetch candle data for this batch
                    batch_candles = self.candle_service.get_candles(
                        exchange=exchange_name,
                        symbol=trade.symbol,
                        timeframe="3m",
                        limit=500,
                        start_ms=current_start_ms,
                        end_ms=batch_end_ms
                    )

                    if batch_candles.empty:
                        print(f"No candle data available for batch {batch_count}")
                        # Move to next batch
                        current_start_ms = batch_end_ms + 1
                        continue

                    # Step 3: Process each candle chronologically
                    candles_in_batch = len(batch_candles)
                    total_candles_processed += candles_in_batch

                    for idx, (candle_time, candle) in enumerate(batch_candles.iterrows()):
                        candle_timestamp = candle_time.to_pydatetime() if hasattr(candle_time, 'to_pydatetime') else candle_time

                        # Sort unfilled orders by execution priority before processing each candle
                        sorted_unfilled_orders = self._sort_orders_by_priority(unfilled_orders, trade, results)

                        for order in sorted_unfilled_orders:
                            # Only check orders that have been created by this candle time
                            if order.created_date and candle_timestamp >= order.created_date:
                                fill_result = self._check_order_fill_against_candle(order, candle, candle_timestamp)

                                if fill_result["filled"]:
                                    # Order was filled
                                    results[order.order_id] = fill_result
                                    # Update order status
                                    from models.order import OrderStatus
                                    order.orderStatus = OrderStatus.FILLED
                                    order.filled_date = candle_timestamp
                                    order.fillPrice = fill_result["fill_price"]
                                    order.filledQuantity = order.quantity
                                    # Remove filled orders from unfilled list
                                    unfilled_orders.remove(order)
                                    print(f"\n✅ Order {order.order_id} filled at {candle_timestamp} Fill Price: {fill_result['fill_price']} {order.orderType} {fill_result['reason']}\n")

                        # Check for trade completion after each candle
                        if self._is_trade_completed(trade, results):
                            print(f"Trade {trade.id_field} completed after processing {total_candles_processed} candles")
                            break

                        # Break if no more unfilled orders
                        if not unfilled_orders:
                            break

                    # Break out of batch loop if trade is completed or no unfilled orders
                    if not unfilled_orders or self._is_trade_completed(trade, results):
                        break

                except Exception as e:
                    traceback.print_exc()
                    print(f"Error processing batch {batch_count}: {e}")
                    # Continue with next batch

                # Move to next batch
                current_start_ms = batch_end_ms + 1

                # Add small delay to avoid overwhelming the API
                time.sleep(0.1)

            # Mark remaining unfilled orders
            for order in unfilled_orders:
                results[order.order_id] = {
                    "status": "New",
                    "filled": False,
                    "fill_price": Decimal("0"),
                    "reason": f"Order not filled during simulation period ({total_candles_processed} candles processed)",
                    "fill_time": None
                }

            logger.info(f"Historical simulation completed for {trade.symbol}: {total_candles_processed} candles processed across {batch_count} batches")

            # Step 5: Update trade details after simulation
            trade.update_trade_details()

            return results

        except Exception as e:
            logger.error(f"Error simulating fills for trade {trade.id_field}: {e}")
            traceback.print_exc()
            # Return all orders as unfilled due to error
            for order in trade.trade_orders:
                results[order.order_id] = {
                    "status": "Unknown",
                    "filled": False,
                    "fill_price": Decimal("0"),
                    "reason": f"Simulation error: {str(e)}"
                }

        return results

    def _simulate_entry_order_fill(self, order, candles_df, current_price: Decimal) -> Dict:
        """
        Simulate entry order fill based on order type and current market conditions.

        Args:
            order: Entry order to simulate
            candles_df: DataFrame with recent candle data
            current_price: Current market price

        Returns:
            dict: Fill simulation result
        """
        order_price = order.price

        # Use string comparison to avoid circular imports
        buy_sell_str = str(order.buySell).lower() if hasattr(order.buySell, 'value') else str(order.buySell).lower()

        # For entry orders, check if the market price has reached the order price
        if "buy" in buy_sell_str:
            # Buy order: fills when market price drops to or below order price
            if current_price <= order_price:
                return {
                    "status": "Filled",
                    "filled": True,
                    "fill_price": min(order_price, current_price),  # Fill at better price if available
                    "reason": f"Market price {current_price} reached buy order price {order_price}"
                }
            else:
                # Check if price touched the order price in recent candles
                for _, candle in candles_df.iterrows():
                    candle_low = Decimal(str(candle['low']))
                    if candle_low <= order_price:
                        return {
                            "status": "Filled",
                            "filled": True,
                            "fill_price": order_price,
                            "reason": f"Order price {order_price} was touched (candle low: {candle_low})"
                        }

                return {
                    "status": "New",
                    "filled": False,
                    "fill_price": Decimal("0"),
                    "reason": f"Market price {current_price} above buy order price {order_price}"
                }

        else:  # SELL order
            # Sell order: fills when market price rises to or above order price
            if current_price >= order_price:
                return {
                    "status": "Filled",
                    "filled": True,
                    "fill_price": max(order_price, current_price),  # Fill at better price if available
                    "reason": f"Market price {current_price} reached sell order price {order_price}"
                }
            else:
                # Check if price touched the order price in recent candles
                for _, candle in candles_df.iterrows():
                    candle_high = Decimal(str(candle['high']))
                    if candle_high >= order_price:
                        return {
                            "status": "Filled",
                            "filled": True,
                            "fill_price": order_price,
                            "reason": f"Order price {order_price} was touched (candle high: {candle_high})"
                        }

                return {
                    "status": "New",
                    "filled": False,
                    "fill_price": Decimal("0"),
                    "reason": f"Market price {current_price} below sell order price {order_price}"
                }

    def _simulate_stop_loss_fill(self, order, candles_df, current_price: Decimal) -> Dict:
        """
        Simulate stop loss order fill.

        Args:
            order: Stop loss order to simulate
            candles_df: DataFrame with recent candle data
            current_price: Current market price

        Returns:
            dict: Fill simulation result
        """
        trigger_price = order.price

        # Use string comparison to avoid circular imports
        buy_sell_str = str(order.buySell).lower() if hasattr(order.buySell, 'value') else str(order.buySell).lower()

        # Stop loss orders trigger when price moves against the position
        if "sell" in buy_sell_str:
            # Sell stop loss (for long position): triggers when price drops to or below trigger price
            if current_price <= trigger_price:
                return {
                    "status": "Filled",
                    "filled": True,
                    "fill_price": current_price,  # Market order execution at current price
                    "reason": f"Stop loss triggered: price {current_price} <= trigger {trigger_price}"
                }
            else:
                # Check if price touched the trigger price in recent candles
                for _, candle in candles_df.iterrows():
                    candle_low = Decimal(str(candle['low']))
                    if candle_low <= trigger_price:
                        return {
                            "status": "Filled",
                            "filled": True,
                            "fill_price": trigger_price,  # Assume fill at trigger price
                            "reason": f"Stop loss triggered in candle (low: {candle_low} <= trigger: {trigger_price})"
                        }

        else:  # BUY stop loss (for short position)
            # Buy stop loss: triggers when price rises to or above trigger price
            if current_price >= trigger_price:
                return {
                    "status": "Filled",
                    "filled": True,
                    "fill_price": current_price,  # Market order execution at current price
                    "reason": f"Stop loss triggered: price {current_price} >= trigger {trigger_price}"
                }
            else:
                # Check if price touched the trigger price in recent candles
                for _, candle in candles_df.iterrows():
                    candle_high = Decimal(str(candle['high']))
                    if candle_high >= trigger_price:
                        return {
                            "status": "Filled",
                            "filled": True,
                            "fill_price": trigger_price,  # Assume fill at trigger price
                            "reason": f"Stop loss triggered in candle (high: {candle_high} >= trigger: {trigger_price})"
                        }

        return {
            "status": "New",
            "filled": False,
            "fill_price": Decimal("0"),
            "reason": f"Stop loss not triggered (current: {current_price}, trigger: {trigger_price})"
        }

    def _simulate_take_profit_fill(self, order, candles_df, current_price: Decimal) -> Dict:
        """
        Simulate take profit order fill.

        Args:
            order: Take profit order to simulate
            candles_df: DataFrame with recent candle data
            current_price: Current market price

        Returns:
            dict: Fill simulation result
        """
        target_price = order.price

        # Use string comparison to avoid circular imports
        buy_sell_str = str(order.buySell).lower() if hasattr(order.buySell, 'value') else str(order.buySell).lower()

        # Take profit orders trigger when price moves in favor of the position
        if "sell" in buy_sell_str:
            # Sell take profit (for long position): triggers when price rises to or above target price
            if current_price >= target_price:
                return {
                    "status": "Filled",
                    "filled": True,
                    "fill_price": target_price,  # Limit order fills at target price
                    "reason": f"Take profit hit: price {current_price} >= target {target_price}"
                }
            else:
                # Check if price touched the target price in recent candles
                for _, candle in candles_df.iterrows():
                    candle_high = Decimal(str(candle['high']))
                    if candle_high >= target_price:
                        return {
                            "status": "Filled",
                            "filled": True,
                            "fill_price": target_price,
                            "reason": f"Take profit hit in candle (high: {candle_high} >= target: {target_price})"
                        }

        else:  # BUY take profit (for short position)
            # Buy take profit: triggers when price drops to or below target price
            if current_price <= target_price:
                return {
                    "status": "Filled",
                    "filled": True,
                    "fill_price": target_price,  # Limit order fills at target price
                    "reason": f"Take profit hit: price {current_price} <= target {target_price}"
                }
            else:
                # Check if price touched the target price in recent candles
                for _, candle in candles_df.iterrows():
                    candle_low = Decimal(str(candle['low']))
                    if candle_low <= target_price:
                        return {
                            "status": "Filled",
                            "filled": True,
                            "fill_price": target_price,
                            "reason": f"Take profit hit in candle (low: {candle_low} <= target: {target_price})"
                        }

        return {
            "status": "New",
            "filled": False,
            "fill_price": Decimal("0"),
            "reason": f"Take profit not hit (current: {current_price}, target: {target_price})"
        }

    def _check_order_fill_against_candle(self, order, candle, candle_timestamp) -> Dict:
        """
        Check if an order would be filled against a specific candle's price action.

        Args:
            order: Order object to check
            candle: Pandas Series containing OHLCV data
            candle_timestamp: Timestamp of the candle

        Returns:
            dict: Fill result with status, filled flag, fill_price, and reason
        """
        # Use string comparison to avoid circular imports
        order_type_str = str(order.orderType).lower() if hasattr(order.orderType, 'value') else str(
            order.orderType).lower()
        buy_sell_str = str(order.buySell).lower() if hasattr(order.buySell, 'value') else str(order.buySell).lower()

        order_price = order.price
        candle_open = Decimal(str(candle['open']))
        candle_high = Decimal(str(candle['high']))
        candle_low = Decimal(str(candle['low']))
        candle_close = Decimal(str(candle['close']))

        # Entry orders
        if "entry" in order_type_str:
            if "buy" in buy_sell_str:
                # Buy entry: fills when price drops to or below order price
                if candle_low <= order_price:
                    return {
                        "status": "Filled",
                        "filled": True,
                        "fill_price": order_price,
                        "reason": f"Buy entry filled at {candle_timestamp} (candle low: {candle_low} <= order: {order_price})",
                        "fill_time": candle_timestamp
                    }
            else:  # SELL entry
                # Sell entry: fills when price rises to or above order price
                if candle_high >= order_price:
                    return {
                        "status": "Filled",
                        "filled": True,
                        "fill_price": order_price,
                        "reason": f"Sell entry filled at {candle_timestamp} (candle high: {candle_high} >= order: {order_price})",
                        "fill_time": candle_timestamp
                    }

        # Stop loss orders
        elif "stop" in order_type_str and "loss" in order_type_str:
            if "sell" in buy_sell_str:
                # Sell stop loss (for long position): triggers when price drops to or below trigger price
                if candle_low <= order_price:
                    return {
                        "status": "Filled",
                        "filled": True,
                        "fill_price": order_price,
                        "reason": f"Stop loss triggered at {candle_timestamp} (candle low: {candle_low} <= trigger: {order_price})",
                        "fill_time": candle_timestamp
                    }
            else:  # BUY stop loss (for short position)
                # Buy stop loss: triggers when price rises to or above trigger price
                if candle_high >= order_price:
                    return {
                        "status": "Filled",
                        "filled": True,
                        "fill_price": order_price,
                        "reason": f"Stop loss triggered at {candle_timestamp} (candle high: {candle_high} >= trigger: {order_price})",
                        "fill_time": candle_timestamp
                    }

        # Take profit orders
        elif "take" in order_type_str and "profit" in order_type_str:
            if "sell" in buy_sell_str:
                # Sell take profit (for long position): triggers when price rises to or above target price
                if candle_high >= order_price:
                    return {
                        "status": "Filled",
                        "filled": True,
                        "fill_price": order_price,
                        "reason": f"Take profit hit at {candle_timestamp} (candle high: {candle_high} >= target: {order_price})",
                        "fill_time": candle_timestamp
                    }
            else:  # BUY take profit (for short position)
                # Buy take profit: triggers when price drops to or below target price
                if candle_low <= order_price:
                    return {
                        "status": "Filled",
                        "filled": True,
                        "fill_price": order_price,
                        "reason": f"Take profit hit at {candle_timestamp} (candle low: {candle_low} <= target: {order_price})",
                        "fill_time": candle_timestamp
                    }

        # Order not filled
        return {
            "status": "New",
            "filled": False,
            "fill_price": Decimal("0"),
            "reason": f"Order not triggered by candle at {candle_timestamp}",
            "fill_time": None
        }

    def _is_trade_completed(self, trade, results) -> bool:
        """
        Check if the trade is completed based on filled orders.

        A trade is considered completed when:
        1. Stop loss orders filled quantity equals entry orders filled quantity, OR
        2. Take profit orders filled quantity equals entry orders filled quantity

        Args:
            trade: Trade object
            results: Current simulation results

        Returns:
            bool: True if trade is completed
        """
        entry_filled_qty = Decimal("0")
        stop_loss_filled_qty = Decimal("0")
        take_profit_filled_qty = Decimal("0")

        for order in trade.trade_orders:
            if order.order_id not in results:
                continue

            result = results[order.order_id]
            if not result["filled"]:
                continue

            # Use string comparison to avoid circular imports
            order_type_str = str(order.orderType).lower() if hasattr(order.orderType, 'value') else str(
                order.orderType).lower()

            if "entry" in order_type_str:
                entry_filled_qty += order.quantity
            elif "stop" in order_type_str and "loss" in order_type_str:
                stop_loss_filled_qty += order.quantity
            elif "take" in order_type_str and "profit" in order_type_str:
                take_profit_filled_qty += order.quantity

        # Trade is completed if position is fully closed by either stop loss or take profit
        if entry_filled_qty > 0:
            if stop_loss_filled_qty >= entry_filled_qty:
                logger.info(f"Trade completed via stop loss: entry={entry_filled_qty}, stop_loss={stop_loss_filled_qty}")
                return True
            elif take_profit_filled_qty >= entry_filled_qty:
                logger.info(f"Trade completed via take profit: entry={entry_filled_qty}, take_profit={take_profit_filled_qty}")
                return True

        return False

    def _sort_orders_by_priority(self, unfilled_orders, trade, results):
        """
        Sort orders by execution priority to ensure realistic trade simulation.
        
        Priority order:
        1. Entry orders (sorted by best price for trade direction)
        2. Stop loss orders (only if position exists)
        3. Take profit orders (only if position exists)
        """
        from models.trade import TradeDirection

        entry_orders = []
        stop_loss_orders = []
        take_profit_orders = []

        # Categorize orders
        for order in unfilled_orders:
            order_type_str = str(order.orderType).lower() if hasattr(order.orderType, 'value') else str(
                order.orderType).lower()

            if "entry" in order_type_str:
                entry_orders.append(order)
            elif "stop" in order_type_str and "loss" in order_type_str:
                stop_loss_orders.append(order)
            elif "take" in order_type_str and "profit" in order_type_str:
                take_profit_orders.append(order)

        # Sort entry orders by price (execution order as price moves)
        if trade.direction == TradeDirection.LONG:
            # For long trades, highest price fills first as price falls
            entry_orders.sort(key=lambda o: o.price, reverse=True)
        else:
            # For short trades, lowest price fills first as price rises
            entry_orders.sort(key=lambda o: o.price, reverse=False)

        # Check if position exists (any entry orders filled)
        position_exists = any(
            order.orderStatus.FILLED
            for order in trade.trade_orders
            if order.orderType.ENTRY
        )

        # Build final sorted list
        sorted_orders = entry_orders.copy()

        # Only include stop/take profit orders if position exists
        if position_exists:
            sorted_orders.extend(stop_loss_orders)
            sorted_orders.extend(take_profit_orders)

        return sorted_orders
