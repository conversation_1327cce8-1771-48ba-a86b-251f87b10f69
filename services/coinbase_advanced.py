import json
import os
import re
import sys
import traceback
from datetime import <PERSON><PERSON><PERSON>, datetime
from decimal import Decimal
from functools import lru_cache
from glob import glob

import pandas as pd
from coinbase.rest import RESTClient

import helper
from models.coinbase_fill import Fill, CoinbaseFill
from models.coinbase_order import CoinbaseOrder
from models.fcm_position import FCMPosition

statements_dir = os.path.expanduser("~/Desktop/statements")
combined_csv_path = os.path.join(statements_dir, "combined_statements.csv")
ORDER_HISTORY_FILE = os.path.join("data", "order_history.json")


class CoinbaseClientManager:
    _client = None
    _api_keys = None

    @staticmethod
    def _load_api_keys():
        """Load API keys from the JSON file (once)."""
        if CoinbaseClientManager._api_keys is None:
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            json_file_path = os.path.join(desktop_path, "JournalKeys.json")

            try:
                with open(json_file_path, "r") as file:
                    CoinbaseClientManager._api_keys = json.load(file)
            except FileNotFoundError:
                raise RuntimeError("Error: API keys file not found.")
            except json.JSONDecodeError:
                raise RuntimeError("Error: Malformed JSON file.")

        return CoinbaseClientManager._api_keys

    @staticmethod
    def get_client():
        """Get or initialize the Coinbase client."""
        if CoinbaseClientManager._client is None:
            keys = CoinbaseClientManager._load_api_keys()
            CoinbaseClientManager._client = RESTClient(api_key=keys["name"], api_secret=keys["privateKey"])
        return CoinbaseClientManager._client


def get_fill_history():
    """Fetch fill history using the Coinbase client, handling pagination."""
    try:
        client = CoinbaseClientManager.get_client()
        filtered_positions = []
        fills = client.get_fills()
        all_fills = []  # To store all fills across pages

        while fills:
            all_fills.extend(fills.fills)
            # filtered_positions.extend(
            #     pos
            #     for pos in fills.fills
            #     if pos["product_id"] == "LC-31JAN25-CDE"
            # )

            if not hasattr(fills, "cursor") or not fills.cursor:
                break

            fills = client.get_fills(cursor=fills.cursor)

        all_fills.sort(key=lambda item: item.trade_time)
        return [CoinbaseOrder(fill) for fill in all_fills]

    except Exception as e:
        print(f"Error get_fill_history: {e}")
        return []


def get_order_history(start_ms=None, end_ms=None):
    """
    Fetch Coinbase Advanced order history from start_ms to end_ms using optimized API parameters.
    Caches results locally. If no date range is provided, fetches all orders.
    """

    def ms_to_iso(ms):
        return helper.ms_to_utc_date(ms).isoformat()

    try:
        # Fallback to local cache
        if False: #os.path.exists(ORDER_HISTORY_FILE):
            try:
                with open(ORDER_HISTORY_FILE, "r") as file:
                    data = json.load(file)
                orders = [CoinbaseOrder(order_data) for order_data in data]
                return orders
            except Exception as read_err:
                # Print stack trace to console
                traceback.print_exc()
                print(f"Error reading cached file: {read_err}")
                return None
        else:
            client = CoinbaseClientManager.get_client()
            all_orders = []

            # Prepare ISO-formatted date range if provided
            start_date = ms_to_iso(start_ms) if start_ms else None
            end_date = ms_to_iso(end_ms) if end_ms else None

            # Initial fetch with date range
            orders = client.list_orders(start_date=start_date, end_date=end_date)
            while orders:
                all_orders.extend(orders.orders)

                if not hasattr(orders, "cursor") or not orders.cursor:
                    break

                orders = client.list_orders(cursor=orders.cursor, start_date=start_date, end_date=end_date)

            # Last fill time is only available if order was filled, and in that case more important than created_time
            # Since created time is when order was created i.e. last week, and fill time is when it was filled i.e. today
            all_orders.sort(key=lambda item: (item.last_fill_time if item.last_fill_time else item.created_time))

            # Ensure data directory exists
            os.makedirs(os.path.dirname(ORDER_HISTORY_FILE), exist_ok=True)

            with open(ORDER_HISTORY_FILE, "w") as file:
                json.dump([order.to_dict() for order in all_orders], file, indent=4)

            return [CoinbaseOrder(order) for order in all_orders]

    except Exception as e:
        # print stack trace
        traceback.print_exc()
        print(f"Error fetching order history: {e}")
        return []


def get_order(order_id):
    """Fetch fill history using the Coinbase client."""
    try:
        client = CoinbaseClientManager.get_client()
        order = client.get_order(order_id=order_id)
        return order
    except Exception as e:
        print(f"Error get_order: {e}")
        return None


def get_futures_positions(username: str = None):
    """Fetch futures positions using the Coinbase client."""
    try:
        from models.trade import Trade

        client = CoinbaseClientManager.get_client()
        futures_positions = client.list_futures_positions()
        futures_positions = [FCMPosition.from_json(pos) for pos in futures_positions.positions]

        # Convert positions directly to trades
        trades = []
        for position in futures_positions:
            try:
                # Create a Trade object directly from FCMPosition
                trade = Trade.from_fcm_position(position, username=username)
                trades.append(trade)
            except Exception as e:
                print(f"❌ Error processing position {position.product_id}: {str(e)}", file=sys.stderr)
                traceback.print_exc()
                # Continue processing other positions even if one fails

        return trades
    except Exception as e:
        print(f"❌ Error get_futures_positions: {str(e)}", file=sys.stderr)
        traceback.print_exc()
        return None


def get_futures_position(symbol):
    """Fetch futures positions using the Coinbase client."""
    try:
        client = CoinbaseClientManager.get_client()
        position = client.get_futures_position(product_id=symbol)
        return position
    except Exception as e:
        print(f"❌ Error get_futures_position: {str(e)}", file=sys.stderr)
        traceback.print_exc()
        return None


def get_all_conversions(symbol):
    """Fetch futures positions using the Coinbase client."""
    try:
        client = CoinbaseClientManager.get_client()
        conversions = client.get_convert_trade()
        return conversions
    except Exception as e:
        print(f"❌ Error get_all_conversions: {str(e)}", file=sys.stderr)
        traceback.print_exc()
        return None


@lru_cache(maxsize=None)  # Cache results indefinitely (or specify a max size)
def get_product(symbol):
    """Fetch futures positions using the Coinbase client."""
    try:
        print(f"[DEBUG] Making fresh API call for {symbol}")  # This should only print once per symbol
        client = CoinbaseClientManager.get_client()
        product = client.get_product(product_id=symbol)
        return product
    except Exception as e:
        print(f"❌ Error get_product: {str(e)}", file=sys.stderr)
        traceback.print_exc()
        return None


def get_futures_balance_summary():
    """Fetch futures balance summary using the Coinbase client."""
    try:
        client = CoinbaseClientManager.get_client()
        summary = client.get_futures_balance_summary()
        return summary
    except Exception as e:
        print(f"❌ Error get_futures_balance_summary: {str(e)}", file=sys.stderr)
        traceback.print_exc()
        return None


def get_wallets(order_id):
    """Fetch fill history using the Coinbase client."""
    try:
        client = CoinbaseClientManager.get_client()
        order = client.list_wa
        return order
    except Exception as e:
        print(f"Error get_order: {e}")
        return None


def create_combined_statement():
    # Find all .csv files in the statements directory
    csv_files = sorted(glob(os.path.join(statements_dir, "*.csv")))

    if not csv_files:
        raise FileNotFoundError(f"No CSV files found in {statements_dir} to combine.")

    combined_df = []

    for i, file in enumerate(csv_files):
        # Read the CSV file, skipping the first three lines
        df = pd.read_csv(file, skiprows=3)

        if i == 0:
            # Keep the header only for the first file
            combined_df.append(df)
        else:
            # Append data without the header
            combined_df.append(df)

    # Concatenate all dataframes
    final_df = pd.concat(combined_df, ignore_index=True)

    # Save to the combined CSV file
    final_df.to_csv(combined_csv_path, index=False)
    print(f"Created combined CSV: {combined_csv_path}")


def load_coinbase_statements():
    if not os.path.exists(combined_csv_path):
        create_combined_statement()
    csv_file = os.path.expanduser(combined_csv_path)
    # Load CSV
    df = pd.read_csv(csv_file)

    # Function to map CSV row to Fill named tuple
    def map_csv_to_fill(row):
        """Maps a CSV row to a Fill named tuple for CoinbaseOrder."""
        return Fill(
            commission=str(helper.clean_decimal(row["Fees and/or Spread"])),
            entry_id=row["ID"],
            liquidity_indicator=None,  # Not in CSV
            price=str(helper.clean_decimal(row["Price at Transaction"])),
            product_id=row["Asset"],
            retail_portfolio_id="statement",  # Not in CSV
            sequence_timestamp=helper.dateStringToDate(row["Timestamp"], helper.infer_date_format(row["Timestamp"])),
            side=determine_side(row["Transaction Type"]),
            size=str(helper.clean_decimal(row["Quantity Transacted"])),
            size_in_quote=False,  # Defaulting to False
            order_id=row["ID"],
            trade_id=None,  # Assuming ID is the trade ID
            trade_time=helper.dateStringToDate(row["Timestamp"], helper.infer_date_format(row["Timestamp"])),
            trade_type=row["Transaction Type"],  # Not in CSV
            user_id=None,  # Not in CSV
            notes=row["Notes"]
        )

    # Convert each CSV row into a CoinbaseOrder object
    orders = [CoinbaseFill(map_csv_to_fill(row)) for _, row in df.iterrows()]
    convert_orders = [order for order in orders if order.trade_type == "Convert"]
    for cbo in convert_orders:
        # Decode the convert
        result = extract_conversion_details(cbo.notes)
        print("Convert Amount From:", result[0])
        print("Convert Currency From:", result[1])
        print("Convert Amount To:", result[2])
        print("Convert Currency To:", result[3])
        convert_order = CoinbaseOrder.from_existing(cbo, Decimal(result[2]), "convert")
        convert_order.product_id = result[3]
        convert_order.trade_time = convert_order.trade_time + timedelta(seconds=3)
        convert_order.sequence_timestamp = convert_order.sequence_timestamp + timedelta(seconds=3)
        orders.append(convert_order)

    return orders


def determine_side(trans_type: str) -> str | None:
    if trans_type == "Receive":
        return "buy"
    elif trans_type == "Advanced Trade Buy":
        return "buy"
    elif trans_type == "Send":
        return "sell"
    elif trans_type == "Convert":
        return "sell"
    elif trans_type == "Pro Withdrawal":
        return "sell"
    elif trans_type == "Pro Deposit":
        return "sell"
    elif trans_type == "Advanced Trade Sell":
        return "sell"
    else:
        return trans_type


def extract_conversion_details(text):
    pattern = r"Converted ([\d\.]+) (\w+) to ([\d\.]+) (\w+)"
    match = re.search(pattern, text)

    if match:
        amount_from = match.group(1)  # "4.08561782"
        currency_from = match.group(2)  # "REN"
        amount_to = match.group(3)  # "0.00272686"
        currency_to = match.group(4)  # "ETH"

        return amount_from, currency_from, amount_to, currency_to
    else:
        return None  # If no match is found


def get_coinbase_orders():
    """
    Retrieves new Coinbase orders that haven't been processed yet.
    
    Returns:
        list: List of Order objects representing new Coinbase orders
    """
    from models.order import Order
    from trades_db import TradesDB
    from models.trade import TradeStatus, Exchange
    import helper

    last_update_ms = TradesDB.get_coinbase_last_order_time()
    current_open_positions = []
    if last_update_ms:
        # Subtract 1 day from last_update_ms to be safe
        last_update_ms = last_update_ms - (24 * 60 * 60 * 1000)
        orders = get_order_history(start_ms=last_update_ms)

        # For non-first imports, we get open positions from the DB since we recreate oldest - newest now
        current_open_positions = TradesDB.get_open_trades_by_exchange(exchange=Exchange.COINBASE)
    else:
        # Set start_date_ms to 180 days ago in ms
        start_date_ms = int((datetime.now() - timedelta(days=90)).timestamp() * 1000)

        end_date_ms = int((datetime.now() - timedelta(days=23)).timestamp() * 1000)
        orders = get_order_history(start_ms=start_date_ms)
        current_open_positions = []

    # Get the set of already processed order IDs
    processed_order_ids = set(TradesDB.getCoinbaseOrderIds())

    # Filter out orders, but allow OPEN orders to be reprocessed
    new_orders = [order for order in orders if
                  (order.coinbase_order_id not in processed_order_ids or order.status == 'OPEN')
                  and order.side != "Receive"]
    # Calculate and display filtering results
    total_orders = len(orders)
    filtered_count = total_orders - len(new_orders)
    print(f"📊 Filtered {filtered_count} already processed orders out of {total_orders} total orders")

    # Convert and return the new orders
    orders = [Order.fromCoinbaseOrder(order) for order in new_orders]

    # Sort orders chronologically by filled_date (or created_date if filled_date is None)
    # This ensures we process the oldest orders first for proper trade reconstruction
    def get_order_date(order):
        return order.filled_date if order.filled_date else order.created_date

    # if last_update:  # Sort current_open_positions by oldest first
    sorted_orders = sorted(orders, key=get_order_date, reverse=False)
    current_open_positions.sort(key=lambda t: t.timeOpen, reverse=False)
    # else:
    #     sorted_orders = sorted(orders, key=get_order_date, reverse=True)
    #     current_open_positions.sort(key=lambda t: t.timeOpen, reverse=True)

    return current_open_positions, sorted_orders, False  # True if last_update is None else False


def get_current_product_price(symbol):
    """Fetch current product price without caching."""
    try:
        print(f"[DEBUG] Making fresh price API call for {symbol}")
        client = CoinbaseClientManager.get_client()
        product = client.get_product(product_id=symbol)
        return product
    except Exception as e:
        print(f"❌ Error get_current_product_price: {str(e)}", file=sys.stderr)
        traceback.print_exc()
        return None
