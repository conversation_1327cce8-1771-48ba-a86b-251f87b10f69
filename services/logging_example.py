# """
# Example of how to configure logging to see Bybit API request logs.
#
# This file demonstrates how to set up logging to capture all API requests
# made by the Bybit service class.
# """
#
# import logging
# import sys
#
# from services.bybit import Bybit
#
#
# def setup_api_logging():
#     """
#     Configure logging to show Bybit API requests.
#
#     This will log all API requests with details including:
#     - HTTP method and URL
#     - Request parameters
#     - Response status codes
#     - Response sizes
#     - Any errors that occur
#     """
#
#     # Create a logger for the services.bybit module
#     bybit_logger = logging.getLogger('services.bybit')
#     bybit_logger.setLevel(logging.INFO)
#
#     # Create console handler with formatting
#     console_handler = logging.StreamHandler(sys.stdout)
#     console_handler.setLevel(logging.INFO)
#
#     # Create formatter
#     formatter = logging.Formatter(
#         '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
#     )
#     console_handler.setFormatter(formatter)
#
#     # Add handler to logger
#     bybit_logger.addHandler(console_handler)
#
#     # Optional: Also log to file
#     file_handler = logging.FileHandler('bybit_api_requests.log')
#     file_handler.setLevel(logging.INFO)
#     file_handler.setFormatter(formatter)
#     bybit_logger.addHandler(file_handler)
#
#     print("Logging configured for Bybit API requests")
#     print("Logs will appear in console and 'bybit_api_requests.log' file")
#
#
# def example_usage():
#     """
#     Example of how to use the logging with Bybit API calls.
#     """
#     # Set up logging first
#     setup_api_logging()
#
#     try:
#         # Now make some API calls - they will be logged
#         print("\n--- Making API calls (check logs) ---")
#
#         # Get executions
#         executions = Bybit.getExecutions()
#         print(f"Retrieved {len(executions)} executions")
#
#         # Get price for a ticker
#         price = Bybit.getPrice("BTCUSDT")
#         print(f"BTC price: {price}")
#
#         # Get account balance
#         balance = Bybit.getAccountBalance()
#         print(f"Account balance: {balance}")
#
#         # Get orders (this might take longer)
#         orders = Bybit.getOrders()
#         print(f"Retrieved {len(orders)} orders")
#
#     except Exception as e:
#         print(f"Error during API calls: {e}")
#
#
# if __name__ == "__main__":
#     example_usage()
