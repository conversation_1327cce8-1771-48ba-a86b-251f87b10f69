"""
Add Order Modal Widget for TradeCraft Trading Journal

This module provides a modal dialog interface for adding new orders to existing trades.
The modal allows users to manually input order details and associate them with a specific trade,
which is useful for adding orders that weren't automatically imported from exchanges or
for manual trade adjustments.

Key Features:
- Modal dialog with comprehensive order entry form
- Trade information display showing context
- Form validation and error handling
- Automatic timezone conversion (EST to UTC)
- Integration with existing trade management system

Components:
- Modal layout with trade info and order entry sections
- Helper functions for datetime handling
- Callback functions for modal interactions and form processing

Usage:
    This widget is designed to be imported and used within a Dash application.
    The modal is triggered by buttons with type "add-order-btn" and requires
    the callbacks to be registered with the main Dash app.

Dependencies:
    - Dash and Dash Bootstrap Components for UI
    - TradeCraft models (Order, BuySell)
    - TradeCraft database layer (TradesDB)
    - Standard libraries for datetime, decimal, and UUID handling
"""

import json
import uuid
from datetime import datetime, timezone
from decimal import Decimal

import dash
import dash_bootstrap_components as dbc
import pytz
from dash import html, dcc, Input, Output, State, ALL

from models.order import Order, BuySell
from trades_db import TradesDB, get_db_cursor, get_db_connection


# ================================
# Helper Functions
# ================================

def get_current_datetime_string():
    """
    Get current date and time in EST timezone formatted for datetime-local input.

    This function provides the default datetime value for the order entry form,
    ensuring all times are consistently handled in Eastern Time (the primary
    trading timezone for US markets).

    Returns:
        str: Current datetime formatted as 'YYYY-MM-DDTHH:MM' in EST timezone
             suitable for HTML datetime-local input fields

    Example:
        >>> get_current_datetime_string()
        '2024-01-15T14:30'
    """
    est = pytz.timezone('America/New_York')
    now = datetime.now(est)
    return now.strftime('%Y-%m-%dT%H:%M')


# ================================
# Modal Layout
# ================================

# Main modal component for adding orders to existing trades
# This modal provides a comprehensive form for entering order details
# and displays relevant trade information for context
modal = dbc.Modal([
    # Modal header with icon and title
    dbc.ModalHeader([
        html.Div([
            html.I(className="bi bi-plus-circle me-2", style={"fontSize": "1.5rem", "color": "#0d6efd"}),
            html.Span("Add Order to Trade", style={"fontSize": "1.25rem", "fontWeight": "500"})
        ], className="d-flex align-items-center"),
    ]),
    dbc.ModalBody([
        # Trade Info Display Section
        # Shows context about the trade being modified (symbol, direction, exchange, order count)
        dbc.Card([
            dbc.CardHeader(html.H6("Trade Information", className="mb-0")),
            dbc.CardBody([
                html.Div(id="add-order-trade-info", className="mb-3")
            ])
        ], className="mb-4"),

        # Order Entry Section
        # Comprehensive form for entering all order details
        dbc.Card([
            dbc.CardHeader(html.H6("New Order Details", className="mb-0")),
            dbc.CardBody([
                # First row: Core order parameters
                dbc.Row([
                    dbc.Col([
                        dbc.Label("Buy/Sell"),
                        dcc.Dropdown(
                            id="add-order-buysell",
                            options=[
                                {"label": "Buy", "value": "Buy"},
                                {"label": "Sell", "value": "Sell"}
                            ],
                            value="Buy",
                            clearable=False
                        )
                    ], width=3),
                    dbc.Col([
                        dbc.Label("Order Type"),
                        dcc.Dropdown(
                            id="add-order-type",
                            options=[
                                {"label": "Market", "value": "Market"},
                                {"label": "Limit", "value": "Limit"},
                                {"label": "Stop", "value": "Stop"},
                                {"label": "Stop Limit", "value": "Stop Limit"}
                            ],
                            value="Market",
                            clearable=False
                        )
                    ], width=3),
                    dbc.Col([
                        dbc.Label("Quantity"),
                        dbc.Input(
                            id="add-order-quantity",
                            type="number",
                            step="any",
                            placeholder="0.00",
                            required=True
                        )
                    ], width=3),
                    dbc.Col([
                        dbc.Label("Price"),
                        dbc.Input(
                            id="add-order-price",
                            type="number",
                            step="any",
                            placeholder="0.00",
                            required=True
                        )
                    ], width=3)
                ], className="mb-3"),
                # Second row: Additional order parameters
                dbc.Row([
                    dbc.Col([
                        dbc.Label("Date & Time (EST)"),
                        dbc.Input(
                            id="add-order-datetime",
                            type="datetime-local",
                            value=get_current_datetime_string()
                        )
                    ], width=4),
                    dbc.Col([
                        dbc.Label("Fee"),
                        dbc.Input(
                            id="add-order-fee",
                            type="number",
                            step="any",
                            placeholder="0.00",
                            value=0
                        )
                    ], width=2),
                    dbc.Col([
                        dbc.Label("Status"),
                        dcc.Dropdown(
                            id="add-order-status",
                            options=[
                                {"label": "Filled", "value": "Filled"},
                                {"label": "Open", "value": "Open"},
                                {"label": "Cancelled", "value": "Cancelled"}
                            ],
                            value="Filled",
                            clearable=False
                        )
                    ], width=3),
                    dbc.Col([
                        dbc.Label("Reduce Only"),
                        dbc.Checklist(
                            id="add-order-reduce",
                            options=[{"label": "", "value": True}],
                            value=[],
                            inline=True,
                            style={"marginTop": "8px"}
                        )
                    ], width=3)
                ])
            ])
        ]),

        # Alert container for validation messages and feedback
        html.Div(id="add-order-alert", className="mt-3"),

        # Hidden store component to maintain trade ID state across callbacks
        dcc.Store(id="add-order-trade-id", data=None)
    ]),
    # Modal footer with action buttons
    dbc.ModalFooter([
        dbc.Button("Cancel", id="cancel-add-order", color="secondary", className="me-2"),
        dbc.Button("Add Order", id="save-add-order", color="primary")
    ])
], id="add-order-modal", size="lg", is_open=False, className="add-order-modal")


# ================================
# Callback Functions
# ================================

def get_callbacks(app):
    """
    Register all callbacks for the add order modal with the Dash application.

    This function defines and registers three main callbacks:
    1. Modal toggle and trade information display
    2. Order saving and validation
    3. Form reset when modal opens

    Args:
        app: The Dash application instance to register callbacks with

    Note:
        This function must be called during application initialization
        to enable the modal's interactive functionality.
    """

    @app.callback(
        [Output("add-order-modal", "is_open"),
         Output("add-order-trade-id", "data"),
         Output("add-order-trade-info", "children")],
        [Input({"type": "add-order-btn", "index": ALL}, "n_clicks"),
         Input("cancel-add-order", "n_clicks")],
        [State("add-order-modal", "is_open"),
         State("add-order-trade-id", "data")],
        prevent_initial_call=True
    )
    def toggle_add_order_modal(add_clicks_list, cancel_clicks, is_open, current_trade_id):
        """
        Toggle the add order modal and populate trade information.

        This callback handles opening/closing the modal and displays relevant
        trade information when a specific trade's "Add Order" button is clicked.

        Args:
            add_clicks_list: List of click counts from all add-order buttons
            cancel_clicks: Number of times cancel button was clicked
            is_open: Current modal open state
            current_trade_id: Currently selected trade ID

        Returns:
            tuple: (modal_open_state, trade_id, trade_info_html)
                - modal_open_state (bool): Whether modal should be open
                - trade_id (str): ID of the trade being modified
                - trade_info_html (html.Div): Trade information display
        """
        ctx = dash.callback_context
        if not ctx.triggered:
            return is_open, current_trade_id, dash.no_update

        triggered_id = ctx.triggered[0]["prop_id"]

        # Handle cancel button - close modal and clear state
        if "cancel-add-order" in triggered_id:
            return False, None, ""

        # Handle add order button clicks from trade list
        if any(add_clicks_list):
            # Extract trade ID from the button that was clicked
            # Button IDs are structured as {"type": "add-order-btn", "index": trade_id}
            button_data = json.loads(triggered_id.split(".")[0])
            trade_id = button_data["index"]

            # Retrieve and display trade information for context
            trade = TradesDB.get_trade_by_id(trade_id=trade_id)
            if trade:
                trade_info = html.Div([
                    html.P([html.Strong("Symbol: "), trade.symbol]),
                    html.P([html.Strong("Direction: "), trade.direction.value]),
                    html.P([html.Strong("Exchange: "), trade.exchange.value]),
                    html.P([html.Strong("Current Orders: "), str(len(trade.trade_orders))])
                ])
                return True, trade_id, trade_info

        return is_open, current_trade_id, dash.no_update

    @app.callback(
        [Output("add-order-alert", "children"),
         Output("add-order-modal", "is_open", allow_duplicate=True)],
        Input("save-add-order", "n_clicks"),
        [State("add-order-trade-id", "data"),
         State("add-order-buysell", "value"),
         State("add-order-type", "value"),
         State("add-order-quantity", "value"),
         State("add-order-price", "value"),
         State("add-order-datetime", "value"),
         State("add-order-fee", "value"),
         State("add-order-status", "value"),
         State("add-order-reduce", "value")],
        prevent_initial_call=True
    )
    def save_order_to_trade(n_clicks, trade_id, buysell, order_type, quantity, price,
                            order_datetime, fee, status, reduce):
        """
        Save the new order to the existing trade and update trade calculations.

        This callback validates form inputs, creates a new Order object, adds it to
        the specified trade, recalculates trade metrics, and saves to the database.

        Args:
            n_clicks: Number of times save button was clicked
            trade_id: ID of the trade to add the order to
            buysell: "Buy" or "Sell" order direction
            order_type: Order type (Market, Limit, Stop, Stop Limit)
            quantity: Order quantity (Decimal)
            price: Order price (Decimal)
            order_datetime: Order datetime string in EST
            fee: Order fee amount (Decimal)
            status: Order status (Filled, Open, Cancelled)
            reduce: List containing True if reduce-only order

        Returns:
            tuple: (alert_component, modal_open_state)
                - alert_component: Success/error message for user feedback
                - modal_open_state: False to close modal on success
        """
        if not n_clicks or not trade_id:
            return dash.no_update, dash.no_update

        try:
            # Validate required fields
            if not quantity or not price:
                return dbc.Alert("Quantity and Price are required", color="danger"), dash.no_update

            # Get the existing trade from database
            trade = TradesDB.get_trade_by_id(trade_id=trade_id)
            if not trade:
                return dbc.Alert("Trade not found", color="danger"), dash.no_update

            # Parse datetime from EST input and convert to UTC for storage
            est = pytz.timezone('America/New_York')
            dt = datetime.strptime(order_datetime, '%Y-%m-%dT%H:%M')
            dt_est = est.localize(dt)
            dt_utc = dt_est.astimezone(timezone.utc)

            # Create new order object with manual ID prefix for tracking
            new_order = Order(
                id_field=None,  # Will be assigned by database
                order_id=f"MANUAL_ADD_{str(uuid.uuid4())}",  # Unique manual order ID
                trade_id=trade_id,
                created_date=dt_utc,
                filled_date=dt_utc if status == "Filled" else None,
                symbol=trade.symbol,
                orderType=order_type,
                orderStatus=status,
                buySell=BuySell.BUY if buysell == "Buy" else BuySell.SELL,
                reduce=bool(reduce),  # Convert list to boolean
                price=Decimal(str(price)),
                fillPrice=Decimal(str(price)) if status == "Filled" else Decimal(0),
                fee=Decimal(str(fee or 0)),
                quantity=Decimal(str(quantity)),
                filledQuantity=Decimal(str(quantity)) if status == "Filled" else Decimal(0),
                # Exchange-specific fields set to None for manual orders
                sierraActivity=None,
                coinbaseOrder=None,
                coinbaseFill=None,
                bybitOrder=None
            )

            # Add order to trade and recalculate trade metrics
            trade.trade_orders.append(new_order)
            trade.update_trade_details()  # Recalculate PnL, quantities, etc.

            # Save updated trade to database
            cursor = get_db_cursor()
            TradesDB.updateTrade(trade, cursor)
            get_db_connection().commit()

            return dbc.Alert("Order added successfully!", color="success"), False

        except Exception as e:
            return dbc.Alert(f"Error adding order: {str(e)}", color="danger"), dash.no_update

    @app.callback(
        [Output("add-order-buysell", "value"),
         Output("add-order-type", "value"),
         Output("add-order-quantity", "value"),
         Output("add-order-price", "value"),
         Output("add-order-datetime", "value"),
         Output("add-order-fee", "value"),
         Output("add-order-status", "value"),
         Output("add-order-reduce", "value"),
         Output("add-order-alert", "children", allow_duplicate=True)],
        Input("add-order-modal", "is_open"),
        prevent_initial_call=True
    )
    def reset_add_order_form(is_open):
        """
        Reset all form fields to default values when modal is opened.

        This ensures a clean form state for each new order entry and prevents
        data from previous entries from persisting in the form.

        Args:
            is_open: Boolean indicating if modal is currently open

        Returns:
            tuple: Default values for all form fields when modal opens,
                   or no_update values when modal closes
        """
        if is_open:
            return ("Buy", "Market", None, None, get_current_datetime_string(), 0, "Filled", [], "")
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
