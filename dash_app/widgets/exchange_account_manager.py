"""
Exchange Account Manager Widget - Add and manage exchange API credentials
"""

import sys
import traceback

import dash
import dash_bootstrap_components as dbc
from dash import html, dcc, Input, Output, State, callback_context as ctx, no_update, ALL
from flask import session

from models.exchange_connection import ExchangeConnection, ConnectionStatus, Exchange
from services.user_balance_service import UserBalanceService
from trades_db import ExchangeConnectionDB
from utils.encryption import encrypt_api_secret, decrypt_api_secret

# ================================
# Exchange Configuration
# ================================

SUPPORTED_EXCHANGES = [
    {"label": "Bybit", "value": "bybit", "icon": "bi-currency-bitcoin"},
    # {"label": "Binance", "value": "binance", "icon": "bi-currency-exchange"},
    {"label": "Coinbase", "value": "coinbase", "icon": "bi-coin"},
    {"label": "Sierra Chart", "value": "sierra", "icon": "bi-coin"},
    # {"label": "Bitget", "value": "bitget", "icon": "bi-graph-up"},
    # {"label": "Hyperliquid", "value": "hyperliquid", "icon": "bi-water"},
    {"label": "BloFin", "value": "blofin", "icon": "bi-bar-chart"},
    # {"label": "WOO X", "value": "woox", "icon": "bi-wallet2"},
    # {"label": "Phemex", "value": "phemex", "icon": "bi-lightning"},
    # {"label": "Deribit", "value": "deribit", "icon": "bi-graph-down"}
]


# ================================
# Helper Functions
# ================================
def save_or_update_exchange(current_accounts, validation_data, validation_html):
    # Extract validation data
    selected_exchange = validation_data["selected_exchange"]
    account_name = validation_data["account_name"]
    api_key = validation_data["api_key"]
    api_secret = validation_data["api_secret"]
    paper_trade = validation_data.get("paper_trade", False)
    is_edit_mode = validation_data["is_edit_mode"]
    connection_id = validation_data["connection_id"]
    user_id = validation_data["user_id"]

    if is_edit_mode and connection_id:
        # Check if this was a skipped validation (credentials unchanged)
        if validation_html is not None and "no validation was needed" in validation_html:
            print(f"🚀 Refreshing display after skipped validation for connection {connection_id}")
            # Just refresh the display since connection was already updated
            user_connections = ExchangeConnectionDB.get_user_exchange_connections(user_id)
            account_cards = [create_exchange_card(conn) for conn in user_connections]
            return account_cards

        # Edit existing connection (normal validation path)
        existing_connection = ExchangeConnectionDB.get_exchange_connection_by_id(connection_id)
        if not existing_connection or existing_connection.user_id != user_id:
            print(f"❌ Connection {connection_id} not found or access denied")
            return current_accounts if current_accounts else []

        # Check if API secret was changed (not masked)
        if api_secret == "••••••••••••••••":
            encrypted_secret = existing_connection.api_secret_encrypted
            print("🔒 API secret unchanged, keeping existing encrypted value")
        else:
            encrypted_secret = encrypt_api_secret(api_secret)
            print("🔒 API secret updated, encrypting new value")

        # Check if API key was changed (not masked)
        if api_key.endswith("...") and len(api_key) < len(existing_connection.api_key):
            api_key = existing_connection.api_key
            print("🔑 API key unchanged, keeping existing value")

        # Update the connection with CONNECTED status
        existing_connection.account_name = account_name
        existing_connection.api_key = api_key
        existing_connection.api_secret_encrypted = encrypted_secret
        existing_connection.paper_trade = paper_trade
        existing_connection.connection_status = ConnectionStatus.CONNECTED.value

        # Save updated connection
        success = ExchangeConnectionDB.update_exchange_connection(existing_connection)
        if success:
            print("=" * 50)
            print("✅ EXCHANGE ACCOUNT UPDATED (VALIDATED):")
            print(f"Exchange: {selected_exchange}")
            print(f"Account Name: {account_name}")
            print(f"Connection ID: {connection_id}")
            print("=" * 50)
        else:
            print(f"❌ Failed to update connection {connection_id}")
            return current_accounts if current_accounts else []

    else:
        # Create new exchange connection with CONNECTED status
        encrypted_secret = encrypt_api_secret(api_secret)

        connection = ExchangeConnection.create_new(
            user_id=user_id,
            exchange=Exchange(selected_exchange),
            account_name=account_name,
            api_key=api_key,
            api_secret_encrypted=encrypted_secret,
            paper_trade=paper_trade
        )
        connection.connection_status = ConnectionStatus.CONNECTED.value

        # Save to database
        connection_id = ExchangeConnectionDB.save_exchange_connection(connection)
        connection.id = connection_id

        print("=" * 50)
        print("✅ NEW EXCHANGE ACCOUNT SAVED (VALIDATED):")
        print(f"Exchange: {selected_exchange}")
        print(f"Account Name: {account_name}")
        print(f"API Key: {api_key}")
        print(f"Connection ID: {connection_id}")
        print("=" * 50)

    # Load all user connections to refresh the display
    user_connections = ExchangeConnectionDB.get_user_exchange_connections(user_id)
    account_cards = [create_exchange_card(conn) for conn in user_connections]
    return account_cards


def create_exchange_card(connection: ExchangeConnection) -> dbc.Card:
    """Create a card displaying exchange account information"""

    # Status styling
    status_color = "success" if connection.connection_status == "connected" else "warning" if connection.connection_status == "pending" else "danger"
    status_icon = "bi-check-circle-fill" if connection.connection_status == "connected" else "bi-clock-fill" if connection.connection_status == "pending" else "bi-x-circle-fill"

    # Find exchange info
    exchange_info = next((ex for ex in SUPPORTED_EXCHANGES if ex["value"] == connection.exchange.value.lower()),
                         {"label": connection.exchange.value.title(), "icon": "bi-gear"})

    return dbc.Card([
        dbc.CardBody([
            dbc.Row([
                # Exchange icon and info
                dbc.Col([
                    html.Div([
                        html.I(className=f"{exchange_info['icon']} me-3",
                               style={"fontSize": "2rem", "color": "#0d6efd"}),
                        html.Div([
                            html.H5(exchange_info["label"], className="mb-1"),
                            html.P(connection.account_name, className="text-muted mb-0", style={"fontSize": "0.9rem"})
                        ])
                    ], className="d-flex align-items-center h-100")
                ], width=6),

                # Status indicator
                dbc.Col([
                    html.Div([
                        html.I(className=f"{status_icon} me-2", style={"color": f"var(--bs-{status_color})"})
                    ], className="d-flex justify-content-center align-items-center h-100")
                ], width=3),

                # Action buttons
                dbc.Col([
                    html.Div([
                        dbc.ButtonGroup([
                            dbc.Button(
                                html.I(className="bi bi-pencil"),
                                id={"type": "edit-exchange", "index": connection.id},
                                color="outline-primary",
                                size="sm",
                                title="Edit Account"
                            ),
                            dbc.Button(
                                html.I(className="bi bi-trash"),
                                id={"type": "delete-exchange", "index": connection.id},
                                color="outline-danger",
                                size="sm",
                                title="Delete Account"
                            )
                        ], size="sm")
                    ], className="d-flex justify-content-end align-items-center h-100")
                ], width=3)
            ], className="align-items-center")
        ])
    ], className="mb-3 shadow-sm")


def create_exchange_selection_grid() -> html.Div:
    """Create a grid of exchange selection buttons"""

    # Create rows of 4 exchanges each
    rows = []
    for i in range(0, len(SUPPORTED_EXCHANGES), 4):
        row_exchanges = SUPPORTED_EXCHANGES[i:i + 4]

        cols = []
        for exchange in row_exchanges:
            col = dbc.Col([
                dbc.Card([
                    html.Div([
                        dbc.CardBody([
                            html.Div([
                                html.I(className=exchange["icon"],
                                       style={"fontSize": "2.5rem", "color": "#0d6efd"}),
                                html.H6(exchange["label"], className="mt-2 mb-0")
                            ], className="text-center")
                        ], className="py-3")
                    ], id={"type": "select-exchange", "index": exchange["value"]},
                        n_clicks=0,
                        style={"cursor": "pointer", "transition": "all 0.2s ease"})
                ], className="h-100 exchange-selection-card")
            ], width=3, className="mb-3")
            cols.append(col)

        # Fill remaining columns if needed
        while len(cols) < 4:
            cols.append(dbc.Col(width=3))

        rows.append(dbc.Row(cols))

    return html.Div(rows)


def _create_error_message(error_text):
    """Create a formatted error message for the validation modal"""
    # Parse common error types and provide helpful suggestions
    suggestions = []

    if "authentication" in error_text.lower() or "invalid" in error_text.lower() or "unauthorized" in error_text.lower():
        suggestions.extend([
            "• Double-check your API key and secret",
            "• Ensure the API key has the required permissions (usually 'Read' permission is sufficient)",
            "• Verify that the API key is not expired"
        ])
    elif "network" in error_text.lower() or "timeout" in error_text.lower() or "connection" in error_text.lower():
        suggestions.extend([
            "• Check your internet connection",
            "• Try again in a few moments",
            "• The exchange API might be temporarily unavailable"
        ])
    elif "rate limit" in error_text.lower():
        suggestions.extend([
            "• Wait a few minutes before trying again",
            "• The exchange is limiting API requests"
        ])
    else:
        suggestions.extend([
            "• Verify your API credentials are correct",
            "• Check that your API key has the necessary permissions",
            "• Ensure you're using the correct exchange (mainnet vs testnet)"
        ])

    return html.Div([
        html.Div([
            html.I(className="bi bi-x-circle-fill me-3",
                   style={"fontSize": "3rem", "color": "#dc3545"}),
            html.Div([
                html.H5("❌ Connection Failed", className="text-danger mb-2"),
                html.P("Unable to connect to the exchange API.", className="mb-2"),
                html.P(f"Error: {error_text}", className="text-muted small mb-0")
            ])
        ], className="d-flex align-items-center mb-3"),
        html.Hr(),
        html.Div([
            html.H6("💡 Suggestions to fix this:", className="mb-2"),
            html.Ul([html.Li(suggestion) for suggestion in suggestions], className="mb-0")
        ]),
        html.Hr(),
        html.P("You can retry the connection or save the account anyway (connection can be tested later).",
               className="text-center text-muted mb-0")
    ])


# ================================
# Main Layout Components
# ================================

# Exchange accounts display section
accounts_section = html.Div([
    html.Div([
        html.H4("API Manager", className="mb-1"),
        html.P("Manage your connected exchange accounts below", className="text-muted mb-4"),
        dbc.Button([
            html.I(className="bi bi-plus-circle me-2"),
            "Add new exchange"
        ], id="add-exchange-btn", color="primary", className="mb-4", n_clicks=0)
    ]),

    # Accounts list container
    html.Div(id="exchange-accounts-container", children=[
        # Will be populated by callback with real data
    ])
])

# Add Exchange Modal
add_exchange_modal = dbc.Modal([
    dbc.ModalHeader([
        html.Div([
            html.I(id="modal-header-icon", className="bi bi-plus-circle me-2", style={"fontSize": "1.5rem", "color": "#0d6efd"}),
            html.Span(id="modal-header-text", children="Add a new exchange", style={"fontSize": "1.25rem", "fontWeight": "500"})
        ], className="d-flex align-items-center"),
    ]),
    dbc.ModalBody([
        # Progress indicator
        html.Div([
            dbc.Row([
                dbc.Col([
                    html.Div([
                        html.Div("1", id="step-1-number", className="step-number active"),
                        html.Span("Select an exchange", id="step-1-text", className="step-text")
                    ], className="step-item")
                ], width=4),
                dbc.Col([
                    html.Div([
                        html.Div("2", id="step-2-number", className="step-number"),
                        html.Span("Enter API details", id="step-2-text", className="step-text")
                    ], className="step-item")
                ], width=4),
                dbc.Col([
                    html.Div([
                        html.Div("3", id="step-3-number", className="step-number"),
                        html.Span("Sync data", id="step-3-text", className="step-text")
                    ], className="step-item")
                ], width=4)
            ], className="mb-4")
        ], id="modal-progress-indicator"),

        # Step 1: Exchange Selection
        html.Div([
            html.H5("Select an Exchange", className="mb-3"),
            create_exchange_selection_grid()
        ], id="step-1-exchange-selection"),

        # Step 2: API Details Form
        html.Div([
            html.Div([
                dbc.Button([
                    html.I(className="bi bi-arrow-left me-2"),
                    "Return to the previous step"
                ], id="back-to-step-1", color="link", className="p-0 mb-3")
            ]),

            html.Div(id="selected-exchange-info"),

            dbc.Form([
                dbc.Row([
                    dbc.Col([
                        dbc.Label("Account name *", className="fw-bold"),
                        dbc.Input(
                            id="exchange-account-name",
                            type="text",
                            placeholder="Enter a name for this account",
                            className="mb-3"
                        )
                    ], width=12)
                ]),
                dbc.Row([
                    dbc.Col([
                        dbc.Label("API key *", className="fw-bold"),
                        dbc.Input(
                            id="exchange-api-key",
                            type="text",
                            placeholder="Enter your API key",
                            className="mb-3"
                        )
                    ], width=12)
                ]),
                dbc.Row([
                    dbc.Col([
                        dbc.Label("Secret key *", className="fw-bold"),
                        dbc.Input(
                            id="exchange-api-secret",
                            type="password",
                            placeholder="Enter your API secret",
                            className="mb-3"
                        )
                    ], width=12)
                ]),
                dbc.Row([
                    dbc.Col([
                        html.Div([
                            dbc.Label("Paper Trading", className="fw-bold mb-2"),
                            html.Div([
                                dbc.Switch(
                                    id="exchange-mock-execution",
                                    value=False,
                                    className="me-2"
                                ),
                                html.Span("Enable mock/paper trading mode", className="text-muted", style={"fontSize": "0.9rem"})
                            ], className="d-flex align-items-center"),
                            html.Small("When enabled, orders will be simulated instead of placed on the exchange",
                                       className="text-muted d-block mt-1", style={"fontSize": "0.8rem"})
                        ], className="mb-3")
                    ], width=12)
                ])
            ])
        ], id="step-2-api-details", style={"display": "none"}),

        # Store for selected exchange
        dcc.Store(id="selected-exchange-store", data=None),
        dcc.Store(id="current-modal-step", data=1)
    ]),
    dbc.ModalFooter([
        dbc.Button("Cancel", id="cancel-add-exchange", color="secondary", className="me-2"),
        dbc.Button(id="connect-exchange", children="Connect", color="primary", style={"display": "none"})
    ])
], id="add-exchange-modal", size="lg", className="add-exchange-modal")

# Delete Confirmation Modal
delete_confirmation_modal = dbc.Modal([
    dbc.ModalHeader([
        html.Div([
            html.I(className="bi bi-exclamation-triangle-fill me-2", style={"fontSize": "1.5rem", "color": "#dc3545"}),
            html.Span("Confirm Deletion", style={"fontSize": "1.25rem", "fontWeight": "500"})
        ], className="d-flex align-items-center"),
    ]),
    dbc.ModalBody([
        html.Div([
            html.P([
                "⚠️ ", html.Strong("WARNING:"), " You are about to delete this exchange connection!"
            ], className="text-danger mb-3"),
            html.Div(id="delete-confirmation-message", className="mb-3"),
            html.P([
                html.Strong("This action cannot be undone."),
                " The connection and all associated data will be permanently removed."
            ], className="text-warning"),
        ])
    ]),
    dbc.ModalFooter([
        dbc.Button("Cancel", id="cancel-delete-exchange", color="secondary", className="me-2"),
        dbc.Button("Yes, Delete Connection", id="confirm-delete-exchange", color="danger")
    ])
], id="delete-confirmation-modal", is_open=False)

# Success/Error Toast for feedback
delete_feedback_toast = dbc.Toast(
    id="delete-feedback-toast",
    header="Exchange Connection",
    is_open=False,
    dismissable=True,
    duration=4000,
    style={"position": "fixed", "top": 66, "right": 10, "width": 350, "z-index": 9999}
)

# Connection Validation Modal
connection_validation_modal = dbc.Modal([
    dbc.ModalHeader([
        html.Div([
            html.I(id="validation-modal-icon", className="bi bi-wifi me-2", style={"fontSize": "1.5rem"}),
            html.Span("API Connection Test", style={"fontSize": "1.25rem", "fontWeight": "500"})
        ], className="d-flex align-items-center"),
    ]),
    dbc.ModalBody([
        html.Div(id="validation-message-container", children=[
            dbc.Spinner(
                html.Div([
                    html.P("Testing API connection...", className="mb-2"),
                    html.P("This may take a few seconds.", className="text-muted mb-0")
                ], className="text-center"),
                size="sm"
            )
        ])
    ]),
    dbc.ModalFooter([
        dbc.Button("Close", id="close-validation-modal", color="secondary", style={"display": "none"}),
        dbc.Button("Retry Connection", id="retry-connection", color="primary", style={"display": "none"}),
        dbc.Button("Save Anyway", id="save-anyway", color="warning", style={"display": "none"})
    ])
], id="connection-validation-modal", is_open=False, backdrop="static", keyboard=False)

# Main widget layout
layout = html.Div([
    accounts_section,
    add_exchange_modal,
    delete_confirmation_modal,
    connection_validation_modal,
    delete_feedback_toast,
    # Store for connection to delete
    dcc.Store(id="connection-to-delete", data=None),
    # Store for connection to edit
    dcc.Store(id="connection-to-edit", data=None),
    # Store to track if modal is in edit mode
    dcc.Store(id="modal-edit-mode", data=False),
    # Store for validation data
    dcc.Store(id="validation-data", data=None)
])


# ================================
# Callback Functions
# ================================

def get_callbacks(app: dash.Dash) -> None:
    """Register all callbacks for the exchange account manager"""

    # Load User Exchange Connections on Page Load
    @app.callback(
        Output("exchange-accounts-container", "children"),
        [Input("exchange-accounts-container", "id")],  # Trigger on component load
        prevent_initial_call=False
    )
    def load_user_exchange_connections(_):
        """Load and display user's existing exchange connections"""
        # Check if user is logged in
        if "user_id" not in session:
            return [
                html.Div([
                    html.I(className="bi bi-info-circle me-2"),
                    "Please log in to view your exchange connections."
                ], className="text-muted text-center py-4")
            ]

        user_id = session["user_id"]

        try:
            # Load user's exchange connections
            user_connections = ExchangeConnectionDB.get_user_exchange_connections(user_id)

            if not user_connections:
                return [
                    html.Div([
                        html.I(className="bi bi-plus-circle me-2"),
                        "No exchange connections yet. Click 'Add new exchange' to get started."
                    ], className="text-muted text-center py-4")
                ]

            # Create cards for each connection
            account_cards = [create_exchange_card(conn) for conn in user_connections]
            return account_cards

        except Exception as e:
            print(f"❌ Error loading exchange connections: {e}")
            traceback.print_exc()
            return [
                html.Div([
                    html.I(className="bi bi-exclamation-triangle me-2"),
                    f"Error loading connections: {str(e)}"
                ], className="text-danger text-center py-4")
            ]

    # Handle Form Submission with API Validation
    @app.callback(
        [Output("connection-validation-modal", "is_open"),
         Output("validation-data", "data")],
        [Input("connect-exchange", "n_clicks")],
        [State("selected-exchange-store", "data"),
         State("exchange-account-name", "value"),
         State("exchange-api-key", "value"),
         State("exchange-api-secret", "value"),
         State("exchange-mock-execution", "value"),
         State("modal-edit-mode", "data"),
         State("connection-to-edit", "data")],
        prevent_initial_call=True
    )
    def handle_exchange_connection_form_submit(clicks, selected_exchange, account_name, api_key, api_secret, paper_trade, is_edit_mode, connection_id):
        """Handle the exchange connection form submission with API validation"""
        if not clicks or not all([selected_exchange, account_name, api_key, api_secret]):
            return no_update, no_update

        # Check if user is logged in
        if "user_id" not in session:
            print("❌ User not logged in - cannot save exchange connection")
            return no_update, no_update

        user_id = session["user_id"]

        # Store form data for validation
        validation_data = {
            "selected_exchange": selected_exchange,
            "account_name": account_name,
            "api_key": api_key,
            "api_secret": api_secret,
            "paper_trade": paper_trade,
            "is_edit_mode": is_edit_mode,
            "connection_id": connection_id,
            "user_id": user_id
        }

        # Open validation modal and start testing
        return True, validation_data

    # Toggle Add Exchange Modal
    @app.callback(
        [Output("add-exchange-modal", "is_open"),
         Output("connection-to-edit", "data", allow_duplicate=True),
         Output("modal-edit-mode", "data", allow_duplicate=True),
         Output("modal-header-icon", "className"),
         Output("modal-header-text", "children"),
         Output("connect-exchange", "children"),
         Output("modal-progress-indicator", "style", allow_duplicate=True),
         Output("back-to-step-1", "style", allow_duplicate=True),
         Output("selected-exchange-store", "data", allow_duplicate=True),
         Output("step-1-exchange-selection", "style", allow_duplicate=True),
         Output("step-2-api-details", "style", allow_duplicate=True),
         Output("connect-exchange", "style", allow_duplicate=True),
         Output("current-modal-step", "data", allow_duplicate=True)],
        [Input("add-exchange-btn", "n_clicks"),
         Input("cancel-add-exchange", "n_clicks"),
         Input("connect-exchange", "n_clicks")],
        [State("add-exchange-modal", "is_open")],
        prevent_initial_call=True
    )
    def toggle_add_exchange_modal(add_clicks, cancel_clicks, connect_clicks, is_open):
        """Toggle the add exchange modal and reset to add mode when opening"""
        if add_clicks or cancel_clicks or connect_clicks:
            if ctx.triggered_id == "add-exchange-btn":
                # Opening modal in add mode
                return (
                    not is_open,  # Toggle modal
                    None,  # Clear edit connection
                    False,  # Set to add mode
                    "bi bi-plus-circle me-2",  # Add icon
                    "Add a new exchange",  # Add title
                    "Connect",  # Connect button text
                    {"display": "block"},  # Show progress indicator
                    {"display": "block"},  # Show back button (will be controlled by other callbacks)
                    None,  # Clear selected exchange
                    {"display": "block"},  # Show step 1
                    {"display": "none"},  # Hide step 2
                    {"display": "none"},  # Hide connect button
                    1  # Reset to step 1
                )
            else:
                # Closing modal
                return (
                    not is_open,  # Toggle modal
                    None,  # Clear edit connection
                    False,  # Reset to add mode
                    dash.no_update,
                    dash.no_update,
                    dash.no_update,
                    dash.no_update,
                    dash.no_update,
                    None,  # Clear selected exchange
                    {"display": "block"},  # Reset to step 1
                    {"display": "none"},  # Hide step 2
                    {"display": "none"},  # Hide connect button
                    1  # Reset to step 1
                )
        return is_open, no_update, no_update, no_update, no_update, no_update, no_update, no_update, no_update

    # Handle Edit Button Clicks - Open Edit Modal
    @app.callback(
        [Output("add-exchange-modal", "is_open", allow_duplicate=True),
         Output("connection-to-edit", "data"),
         Output("modal-edit-mode", "data"),
         Output("modal-header-icon", "className", allow_duplicate=True),
         Output("modal-header-text", "children", allow_duplicate=True),
         Output("connect-exchange", "children", allow_duplicate=True),
         Output("modal-progress-indicator", "style", allow_duplicate=True),
         Output("back-to-step-1", "style", allow_duplicate=True),
         Output("selected-exchange-store", "data", allow_duplicate=True),
         Output("step-1-exchange-selection", "style", allow_duplicate=True),
         Output("step-2-api-details", "style", allow_duplicate=True),
         Output("connect-exchange", "style", allow_duplicate=True),
         Output("current-modal-step", "data", allow_duplicate=True)],
        [Input({"type": "edit-exchange", "index": ALL}, "n_clicks")],
        [State("add-exchange-modal", "is_open")],
        prevent_initial_call=True
    )
    def toggle_edit_exchange_modal(edit_clicks_list, is_open):
        """Handle edit button clicks and open modal in edit mode"""
        # Check if any edit button was clicked
        if not edit_clicks_list or not any(edit_clicks_list) or not ctx.triggered:
            return (no_update, no_update, no_update, no_update, no_update, no_update,
                    no_update, no_update, no_update, no_update, no_update, no_update, no_update)

        # Check if user is logged in
        if "user_id" not in session:
            return (no_update, no_update, no_update, no_update, no_update, no_update,
                    no_update, no_update, no_update, no_update, no_update, no_update, no_update)

        user_id = session["user_id"]

        try:
            # Find which edit button was clicked
            import json
            triggered = ctx.triggered[0]
            triggered_id = json.loads(triggered["prop_id"].split(".")[0])
            connection_id = triggered_id["index"]

            # Get the connection details from database
            connection = ExchangeConnectionDB.get_exchange_connection_by_id(connection_id)
            if not connection or connection.user_id != user_id:
                print(f"❌ Connection {connection_id} not found or access denied")
                return (no_update, no_update, no_update, no_update, no_update, no_update,
                        no_update, no_update, no_update, no_update, no_update, no_update, no_update)

            print(f"✅ Opening edit modal for connection: {connection.account_name} ({connection.exchange})")

            return (
                True,  # Open modal
                connection_id,  # Store connection ID for editing
                True,  # Set edit mode
                "bi bi-pencil me-2",  # Edit icon
                "Update Exchange Info",  # Edit title
                "Update",  # Update button text
                {"display": "none"},  # Hide progress indicator
                {"display": "none"},  # Hide back button
                connection.exchange.value,  # Set selected exchange
                {"display": "none"},  # Hide step 1 (exchange selection)
                {"display": "block"},  # Show step 2 (API details)
                {"display": "inline-block"},  # Show connect button
                2  # Set current step to 2
            )

        except Exception as e:
            print(f"❌ Error opening edit modal: {e}")
            return (no_update, no_update, no_update, no_update, no_update, no_update,
                    no_update, no_update, no_update, no_update, no_update, no_update, no_update)

    # Update Progress Indicator Based on Current Step
    @app.callback(
        [Output("step-1-number", "className"),
         Output("step-2-number", "className"),
         Output("step-3-number", "className")],
        [Input("current-modal-step", "data")],
        prevent_initial_call=True
    )
    def update_progress_indicator(current_step):
        """Update progress indicator to highlight the current step"""
        if current_step == 1:
            return (
                "step-number active",  # Step 1: Active
                "step-number",  # Step 2: Inactive
                "step-number"  # Step 3: Inactive
            )
        elif current_step == 2:
            return (
                "step-number completed",  # Step 1: Completed (we'll add this style)
                "step-number active",  # Step 2: Active
                "step-number"  # Step 3: Inactive
            )
        elif current_step == 3:
            return (
                "step-number completed",  # Step 1: Completed
                "step-number completed",  # Step 2: Completed
                "step-number active"  # Step 3: Active
            )
        else:
            # Default to step 1 active
            return (
                "step-number active",  # Step 1: Active
                "step-number",  # Step 2: Inactive
                "step-number"  # Step 3: Inactive
            )

    # Handle Exchange Selection
    @app.callback(
        [Output("selected-exchange-store", "data"),
         Output("step-1-exchange-selection", "style"),
         Output("step-2-api-details", "style"),
         Output("connect-exchange", "style"),
         Output("current-modal-step", "data")],
        [Input({"type": "select-exchange", "index": ALL}, "n_clicks")],
        [State("selected-exchange-store", "data")],
        prevent_initial_call=True
    )
    def handle_exchange_selection(clicks_list, current_selection):
        """Handle exchange selection and move to step 2"""
        if not any(clicks_list):
            return no_update, no_update, no_update, no_update, no_update

        # Find which exchange was clicked
        triggered = ctx.triggered[0]
        if triggered["prop_id"] == ".":
            return no_update, no_update, no_update, no_update, no_update

        # Extract exchange from triggered ID
        import json
        triggered_id = json.loads(triggered["prop_id"].split(".")[0])
        selected_exchange = triggered_id["index"]

        return (
            selected_exchange,
            {"display": "none"},  # Hide step 1
            {"display": "block"},  # Show step 2
            {"display": "inline-block"},  # Show connect button
            2  # Current step
        )

    # Handle Back to Step 1
    @app.callback(
        [Output("step-1-exchange-selection", "style", allow_duplicate=True),
         Output("step-2-api-details", "style", allow_duplicate=True),
         Output("connect-exchange", "style", allow_duplicate=True),
         Output("current-modal-step", "data", allow_duplicate=True)],
        [Input("back-to-step-1", "n_clicks")],
        prevent_initial_call=True
    )
    def back_to_step_1(clicks):
        """Go back to exchange selection"""
        if clicks:
            return (
                {"display": "block"},  # Show step 1
                {"display": "none"},  # Hide step 2
                {"display": "none"},  # Hide connect button
                1  # Current step
            )
        return no_update, no_update, no_update, no_update

    # Update Selected Exchange Info
    @app.callback(
        Output("selected-exchange-info", "children"),
        [Input("selected-exchange-store", "data")],
        prevent_initial_call=True
    )
    def update_selected_exchange_info(selected_exchange):
        """Update the selected exchange information display"""
        if not selected_exchange:
            return ""

        # Find exchange info
        exchange_info = next((ex for ex in SUPPORTED_EXCHANGES if ex["value"] == selected_exchange), None)
        if not exchange_info:
            return ""

        return html.Div([
            html.Div([
                html.I(className=exchange_info["icon"], style={"fontSize": "2rem", "marginRight": "1rem"}),
                html.Div([
                    html.H5(f"Enter {exchange_info['label']} API details", className="mb-1"),
                    html.P("Follow the instructions to input your account read-only API details",
                           className="text-muted mb-0")
                ])
            ], className="d-flex align-items-center mb-4")
        ])

    # Pre-populate Form Fields in Edit Mode
    @app.callback(
        [Output("exchange-account-name", "value"),
         Output("exchange-api-key", "value"),
         Output("exchange-api-secret", "value"),
         Output("exchange-mock-execution", "value")],
        [Input("connection-to-edit", "data")],
        prevent_initial_call=True
    )
    def populate_edit_form(connection_id):
        """Pre-populate form fields when editing an existing connection"""
        if not connection_id:
            return "", "", "", False

        # Check if user is logged in
        if "user_id" not in session:
            return "", "", "", False

        user_id = session["user_id"]

        try:
            # Get the connection details from database
            connection = ExchangeConnectionDB.get_exchange_connection_by_id(connection_id)
            if not connection or connection.user_id != user_id:
                print(f"❌ Connection {connection_id} not found or access denied")
                return "", "", "", False

            # Decrypt the API secret for editing
            decrypted_secret = decrypt_api_secret(connection.api_secret_encrypted)

            # Mask the secret for security (show partial)
            masked_secret = "••••••••••••••••"  # Always mask the secret

            print(f"✅ Pre-populating form for connection: {connection.account_name}")

            return (
                connection.account_name,
                connection.api_key,
                masked_secret,
                connection.paper_trade
            )

        except Exception as e:
            print(f"❌ Error pre-populating form: {e}")
            return "", "", "", False

    # Reset Validation Modal to Default State
    @app.callback(
        [Output("validation-message-container", "children", allow_duplicate=True),
         Output("validation-modal-icon", "className", allow_duplicate=True),
         Output("validation-modal-icon", "style", allow_duplicate=True),
         Output("close-validation-modal", "style", allow_duplicate=True),
         Output("retry-connection", "style", allow_duplicate=True),
         Output("save-anyway", "style", allow_duplicate=True)],
        [Input("connection-validation-modal", "is_open")],
        prevent_initial_call=True
    )
    def reset_validation_modal_state(is_open):
        """Reset validation modal to default loading state when opened"""
        if is_open:
            # Reset to default loading state
            default_content = dbc.Spinner(
                html.Div([
                    html.P("Testing API connection...", className="mb-2"),
                    html.P("This may take a few seconds.", className="text-muted mb-0")
                ], className="text-center"),
                size="sm"
            )

            return (
                default_content,  # Reset message container
                "bi bi-wifi me-2",  # Reset icon
                {"fontSize": "1.5rem"},  # Reset icon style
                {"display": "none"},  # Hide close button
                {"display": "none"},  # Hide retry button
                {"display": "none"}  # Hide save anyway button
            )

        return no_update, no_update, no_update, no_update, no_update, no_update

    # Handle API Connection Validation
    @app.callback(
        [Output("validation-message-container", "children"),
         Output("validation-modal-icon", "className"),
         Output("validation-modal-icon", "style"),
         Output("close-validation-modal", "style"),
         Output("retry-connection", "style"),
         Output("save-anyway", "style")],
        [Input("validation-data", "data")],
        prevent_initial_call=True
    )
    def perform_api_validation(validation_data):
        """Perform API connection validation when modal opens"""
        if not validation_data:
            return no_update, no_update, no_update, no_update, no_update, no_update

        try:
            # Extract validation data
            selected_exchange = validation_data["selected_exchange"]
            account_name = validation_data["account_name"]
            api_key = validation_data["api_key"]
            api_secret = validation_data["api_secret"]
            paper_trade = validation_data.get("paper_trade", False)
            is_edit_mode = validation_data["is_edit_mode"]
            connection_id = validation_data["connection_id"]
            user_id = validation_data["user_id"]

            # Handle edit mode credentials
            if is_edit_mode and connection_id:
                existing_connection = ExchangeConnectionDB.get_exchange_connection_by_id(connection_id)
                if not existing_connection or existing_connection.user_id != user_id:
                    return _create_error_message("Connection not found or access denied"), \
                        "bi bi-x-circle-fill me-2", {"fontSize": "1.5rem", "color": "#dc3545"}, \
                        {"display": "inline-block"}, {"display": "none"}, {"display": "none"}

                # Check if credentials have been modified
                api_key_unchanged = api_key == existing_connection.api_key
                api_secret_unchanged = api_secret == "••••••••••••••••"

                # OPTIMIZATION: Skip API validation if both credentials are unchanged
                if api_key_unchanged and api_secret_unchanged:
                    print(f"🚀 Skipping API validation - credentials unchanged for {account_name}")

                    # Update connection with existing credentials and current status
                    existing_connection.account_name = account_name
                    existing_connection.paper_trade = paper_trade

                    # Save updated connection (keeping existing connection status)
                    success = ExchangeConnectionDB.update_exchange_connection(existing_connection)
                    if success:
                        print(f"✅ Updated connection {connection_id} without API validation")

                        # Return success message without API test
                        success_message = html.Div([
                            html.Div([
                                html.I(className="bi bi-check-circle-fill me-3",
                                       style={"fontSize": "3rem", "color": "#198754"}),
                                html.Div([
                                    html.H5("✅ Connection Updated Successfully!", className="text-success mb-2"),
                                    html.P("Account settings have been updated.", className="mb-2"),
                                    html.P("API credentials were not changed, so no validation was needed.", className="text-muted mb-0")
                                ])
                            ], className="d-flex align-items-center mb-3"),
                            html.Hr(),
                            html.P("The connection has been saved successfully.", className="text-center text-muted mb-0")
                        ])

                        return success_message, \
                            "bi bi-check-circle-fill me-2", {"fontSize": "1.5rem", "color": "#198754"}, \
                            {"display": "inline-block"}, {"display": "none"}, {"display": "none"}
                    else:
                        return _create_error_message("Failed to update connection in database"), \
                            "bi bi-x-circle-fill me-2", {"fontSize": "1.5rem", "color": "#dc3545"}, \
                            {"display": "inline-block"}, {"display": "none"}, {"display": "none"}

                # Credentials have been modified, proceed with validation
                print(f"🔍 Credentials modified, performing API validation for {account_name}")

                # Check if API secret was changed (not masked)
                if api_secret_unchanged:
                    encrypted_secret = existing_connection.api_secret_encrypted
                else:
                    encrypted_secret = encrypt_api_secret(api_secret)

                # Check if API key was changed (not masked)
                if api_key_unchanged:
                    api_key = existing_connection.api_key
            else:
                # New connection
                encrypted_secret = encrypt_api_secret(api_secret)

            # Create temporary connection for testing
            test_connection = ExchangeConnection.create_new(
                user_id=user_id,
                exchange=Exchange(selected_exchange.lower()),
                account_name=account_name,
                api_key=api_key,
                api_secret_encrypted=encrypted_secret,
                paper_trade=paper_trade
            )

            # Test the connection
            print(f"🔍 Testing API connection for {selected_exchange} - {account_name}")
            test_result = UserBalanceService.test_connection(test_connection)

            if test_result["success"]:
                # Connection successful
                success_message = html.Div([
                    html.Div([
                        html.I(className="bi bi-check-circle-fill me-3",
                               style={"fontSize": "3rem", "color": "#198754"}),
                        html.Div([
                            html.H5("✅ Connection Successful!", className="text-success mb-2"),
                            html.P(test_result["message"], className="mb-2"),
                            html.P("Your API credentials are working correctly.", className="text-muted mb-0")
                        ])
                    ], className="d-flex align-items-center mb-3"),
                    html.Hr(),
                    html.P("The connection has been saved successfully.", className="text-center text-muted mb-0")
                ])

                return success_message, \
                    "bi bi-check-circle-fill me-2", {"fontSize": "1.5rem", "color": "#198754"}, \
                    {"display": "inline-block"}, {"display": "none"}, {"display": "none"}
            else:
                # Connection failed
                error_message = _create_error_message(test_result["error"])
                return error_message, \
                    "bi bi-x-circle-fill me-2", {"fontSize": "1.5rem", "color": "#dc3545"}, \
                    {"display": "inline-block"}, {"display": "inline-block"}, {"display": "inline-block"}

        except Exception as e:
            print(f"❌ Error during API validation: {str(e)}", file=sys.stderr)
            traceback.print_exc()
            error_message = _create_error_message(f"Validation error: {str(e)}")
            return error_message, \
                "bi bi-x-circle-fill me-2", {"fontSize": "1.5rem", "color": "#dc3545"}, \
                {"display": "inline-block"}, {"display": "inline-block"}, {"display": "inline-block"}

    # Handle Validation Modal Actions
    @app.callback(
        [Output("connection-validation-modal", "is_open", allow_duplicate=True),
         Output("add-exchange-modal", "is_open", allow_duplicate=True),
         Output("validation-data", "data", allow_duplicate=True)],
        [Input("close-validation-modal", "n_clicks"),
         Input("retry-connection", "n_clicks")],
        [State("connection-validation-modal", "is_open"),
         State("validation-message-container", "children")],
        prevent_initial_call=True
    )
    def handle_validation_modal_actions(close_clicks, retry_clicks, is_open, validation_message):
        """Handle close and retry actions in validation modal"""
        if ctx.triggered_id == "close-validation-modal":
            # Check if validation was successful to determine whether to close both modals
            validation_html = str(validation_message) if validation_message else ""
            if ("Connection Successful" in validation_html or "Connection Updated Successfully" in validation_html) and "✅" in validation_html:
                return False, False, None  # Close both modals on successful validation/update, clear validation data
            else:
                return False, no_update, None  # Close validation modal only on error, keep add modal open, clear validation data
        elif ctx.triggered_id == "retry-connection":
            return False, no_update, None  # Close validation modal to retry, keep add modal open, clear validation data
        return is_open, no_update, no_update

    # Handle Save Anyway Action
    @app.callback(
        [Output("exchange-accounts-container", "children", allow_duplicate=True),
         Output("connection-validation-modal", "is_open", allow_duplicate=True),
         Output("add-exchange-modal", "is_open", allow_duplicate=True),
         Output("validation-data", "data", allow_duplicate=True)],
        [Input("save-anyway", "n_clicks")],
        [State("validation-data", "data"),
         State("exchange-accounts-container", "children")],
        prevent_initial_call=True
    )
    def handle_save_anyway(save_clicks, validation_data, current_accounts):
        """Save the connection even if validation failed"""
        if not save_clicks or not validation_data:
            return no_update, no_update, no_update, no_update

        try:
            account_cards = save_or_update_exchange(current_accounts, validation_data, None)
            return account_cards, False, False, None  # Close both modals and clear validation data

        except Exception as e:
            print(f"❌ Error saving exchange connection: {e}")
            return current_accounts if current_accounts else [], False, False, None

    # Auto-save connection when validation succeeds
    @app.callback(
        Output("exchange-accounts-container", "children", allow_duplicate=True),
        [Input("validation-message-container", "children")],
        [State("validation-data", "data"),
         State("exchange-accounts-container", "children")],
        prevent_initial_call=True
    )
    def auto_save_on_successful_validation(validation_message, validation_data, current_accounts):
        """Automatically save connection when validation succeeds"""
        if not validation_data or not validation_message:
            return no_update

        # Check if the validation was successful by looking for success indicators
        validation_html = str(validation_message)
        if ("Connection Successful" not in validation_html and "Connection Updated Successfully" not in validation_html) or "✅" not in validation_html:
            return no_update

        try:
            account_cards = save_or_update_exchange(current_accounts, validation_data, validation_html)
            # Return updated accounts list (modal stays open for user to close)
            return account_cards

        except Exception as e:
            print(f"❌ Error auto-saving exchange connection: {e}")
            return current_accounts if current_accounts else []

    # Handle Delete Button Clicks - Open Confirmation Modal
    @app.callback(
        [Output("delete-confirmation-modal", "is_open"),
         Output("delete-confirmation-message", "children"),
         Output("connection-to-delete", "data")],
        [Input({"type": "delete-exchange", "index": ALL}, "n_clicks")],
        [State("delete-confirmation-modal", "is_open")],
        prevent_initial_call=True
    )
    def handle_delete_button_click(delete_clicks_list, is_open):
        """Handle delete button clicks and show confirmation modal"""
        # Check if any delete button was clicked
        if not delete_clicks_list or not any(delete_clicks_list) or not ctx.triggered:
            return no_update, no_update, no_update

        # Check if user is logged in
        if "user_id" not in session:
            return no_update, no_update, no_update

        user_id = session["user_id"]

        try:
            # Get the triggered component info
            triggered_prop_id = ctx.triggered[0]["prop_id"]
            if not triggered_prop_id or triggered_prop_id == ".":
                return no_update, no_update, no_update

            # Parse the component ID to get connection_id
            import json
            triggered_component = json.loads(triggered_prop_id.split(".")[0])
            connection_id = triggered_component["index"]

            # Get the connection details from database
            connection = ExchangeConnectionDB.get_exchange_connection_by_id(connection_id)

            if not connection or connection.user_id != user_id:
                # Security check: only allow deletion of user's own connections
                return no_update, no_update, no_update

            # Create confirmation message
            confirmation_message = html.Div([
                html.P([
                    "Are you sure you want to delete the exchange connection ",
                    html.Strong(f"'{connection.account_name}'"),
                    f" for {connection.exchange.value.title()}?"
                ], className="mb-2"),
                html.P([
                    html.Small([
                        html.Strong("Connection ID: "), f"{connection.id}",
                        html.Br(),
                        html.Strong("Status: "), connection.get_display_status()
                    ], className="text-muted")
                ])
            ])

            return True, confirmation_message, connection_id

        except Exception as e:
            print(f"❌ Error handling delete button click: {e}")
            return no_update, no_update, no_update

    # Handle Confirmation Modal Actions
    @app.callback(
        [Output("delete-confirmation-modal", "is_open", allow_duplicate=True),
         Output("connection-to-delete", "data", allow_duplicate=True)],
        [Input("cancel-delete-exchange", "n_clicks"),
         Input("confirm-delete-exchange", "n_clicks")],
        [State("delete-confirmation-modal", "is_open")],
        prevent_initial_call=True
    )
    def handle_delete_confirmation_modal(cancel_clicks, confirm_clicks, is_open):
        """Handle cancel and confirm actions in delete confirmation modal"""
        if ctx.triggered_id == "cancel-delete-exchange":
            return False, None  # Close modal and clear data
        elif ctx.triggered_id == "confirm-delete-exchange":
            return False, no_update  # Close modal but keep data for deletion
        return is_open, no_update

    # Handle Actual Deletion
    @app.callback(
        [Output("exchange-accounts-container", "children", allow_duplicate=True),
         Output("delete-feedback-toast", "is_open"),
         Output("delete-feedback-toast", "children"),
         Output("delete-feedback-toast", "icon")],
        [Input("confirm-delete-exchange", "n_clicks")],
        [State("connection-to-delete", "data")],
        prevent_initial_call=True
    )
    def perform_exchange_deletion(confirm_clicks, connection_id):
        """Perform the actual deletion of the exchange connection"""
        if not confirm_clicks or connection_id is None:
            return no_update, no_update, no_update, no_update

        # Check if user is logged in
        if "user_id" not in session:
            return no_update, True, "Error: User not logged in", "danger"

        user_id = session["user_id"]

        try:
            # Get connection details before deletion for feedback message
            connection = ExchangeConnectionDB.get_exchange_connection_by_id(connection_id)

            if not connection:
                return no_update, True, "Error: Connection not found", "danger"

            if connection.user_id != user_id:
                return no_update, True, "Error: Unauthorized access", "danger"

            # Perform the deletion
            deletion_success = ExchangeConnectionDB.delete_exchange_connection(connection_id, user_id)

            if deletion_success:
                # Refresh the connections list
                user_connections = ExchangeConnectionDB.get_user_exchange_connections(user_id)

                if not user_connections:
                    account_cards = [
                        html.Div([
                            html.I(className="bi bi-plus-circle me-2"),
                            "No exchange connections yet. Click 'Add new exchange' to get started."
                        ], className="text-muted text-center py-4")
                    ]
                else:
                    account_cards = [create_exchange_card(conn) for conn in user_connections]

                # Success feedback
                success_message = f"Exchange connection '{connection.account_name}' for {connection.exchange.value.title()} has been deleted successfully."

                return account_cards, True, success_message, "success"
            else:
                # Deletion failed
                return no_update, True, "Error: Failed to delete connection", "danger"

        except Exception as e:
            print(f"❌ Error deleting exchange connection: {e}")
            error_message = f"Error deleting connection: {str(e)}"
            return no_update, True, error_message, "danger"


# ================================
# CSS Styles (to be added to assets)
# ================================

EXCHANGE_MANAGER_CSS = """
/* Exchange Account Manager Styles */
.add-exchange-modal .modal-dialog {
    max-width: 800px;
}

.exchange-selection-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
    border-color: #0d6efd !important;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
}

.step-number.active {
    background-color: #0d6efd;
    color: white;
}

.step-number.completed {
    background-color: #198754;
    color: white;
}

.step-text {
    font-size: 0.9rem;
    color: #6c757d;
}

.exchange-accounts-container .card {
    transition: all 0.2s ease;
}

.exchange-accounts-container .card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.exchange-accounts-container .card .row {
    min-height: 60px;
    align-items: center;
}

.exchange-accounts-container .card .col {
    display: flex;
    align-items: center;
}
"""
