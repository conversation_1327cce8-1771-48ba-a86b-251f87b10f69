import json
import sys
import traceback
from datetime import datetime

import dash
import numpy as np
import pandas as pd
import plotly.graph_objs as go
from dash import Output, Input, State, MATCH, set_props, ALL

from dash_app.widgets.info_row_cards import *
from models.order import BuySell
from models.trade import TradeStatus, TradeDirection, Exchange
from trades_db import TradesDB


def get_exchange_icon(exchange):
    """
    Returns an HTML img element with the appropriate exchange logo.

    Args:
        exchange: Exchange enum value

    Returns:
        html.Img: Image element with the exchange logo
    """
    # Map exchange enum values to logo filenames
    exchange_logos = {
        Exchange.BYBIT: "bybit-logo.png",
        Exchange.COINBASE: "coinbase-logo.png",
        Exchange.EDGECME: "edgeclear-logo.png",
        Exchange.BITGET: "bitget-logo.png",
        Exchange.BLOFIN: "blofin-logo.png",
        # Add fallback for unknown exchanges
        Exchange.UNKNOWN: None,
        Exchange.BINANCE: None,
        Exchange.KRAKEN: None,
        Exchange.GEMINI: None,
        Exchange.BITFINEX: None,
        Exchange.HUOBI: None,
        Exchange.KUCOIN: None,
        Exchange.OKX: None,
    }

    logo_filename = exchange_logos.get(exchange)

    if logo_filename:
        return html.Img(
            src=f"/assets/{logo_filename}",
            style={
                "height": "16px",
                "width": "16px",
                "objectFit": "contain"
            },
            title=exchange.value
        )
    else:
        # Return a generic exchange icon or text for unknown exchanges
        return html.Span(
            "🏦",
            style={"fontSize": "14px"},
            title=exchange.value
        )


def format_trade_details(trade):
    return html.Div([
        html.H5([
            get_exchange_icon(trade.exchange),
            html.Span(f" {trade.symbol} ({trade.direction.value})", style={"marginLeft": "5px"})
        ], className="mb-3", style={"display": "flex", "alignItems": "center"}),
        dbc.Row([
            # Column 1 – Position Exchange, Dates, Status Info
            dbc.Col([
                html.H3("Time & Place"),
                html.Div([html.Strong("Status: "), trade.status.value]),
                html.Div([
                    html.Strong("Exchange: "),
                    get_exchange_icon(trade.exchange),
                    html.Span(f" {trade.exchange.value}", style={"marginLeft": "5px"})
                ], style={"display": "flex", "alignItems": "center"}),
                html.Div([html.Strong("Opened: "), helper.formatDate(trade.created_date)]),
                html.Div([html.Strong("Closed: "), helper.formatDate(trade.closed_date) if trade.closed_date else "—"]),
                html.Div([html.Strong("Duration: "), trade.getTradeDurationString()]),
                html.Div([html.Strong("Last Update: "), helper.formatDate(trade.last_update)]),
            ], width=4, className="mb-4"),

            # Column 2 – Position Price and Quantity Info
            dbc.Col([
                html.H3("Position Info"),
                html.Div([html.Strong("Quantity: "), trade.tradeQty]),
                html.Div([html.Strong("Open Qty: "), trade.openQty]),
                html.Div([html.Strong("Avg Open Price: "), "$" + str(trade.avgOpenPrice)]),
                html.Div([html.Strong("Avg Close Price: "), "$" + str(trade.avgClosePrice)]),
                html.Div([html.Strong("Notional: "), "$" + str(trade.notional)]),
                html.Div([html.Strong("Leverage: "), trade.leverage]),
            ], width=4),

            # Column 3 – Risk & Performance
            dbc.Col([
                html.H3("Risk & Performance"),
                html.Div([html.Strong("Risk %: "), trade.riskPercent]),
                html.Div([html.Strong("Risk Amount: "), "$" + str(trade.riskAmt)]),
                html.Div([html.Strong("Account Balance: "), "$" + str(trade.accountBalance)]),
                html.Div([html.Strong("Profit: "), "$" + str(trade.profit)]),
                html.Div([html.Strong("Fees: "), "$" + str(trade.fees)]),
            ], width=4),
        ]),
        dbc.Row([
            # New Row – Notes & Chart
            dbc.Col([
                html.H3("Notes"),
                html.Div(
                    id={"type": "trade-notes", "index": trade.id_field},
                    children=_format_notes_with_delete_buttons(trade.notes, trade.id_field),
                    style={"minHeight": "0px", "marginBottom": "5px"}
                ),
                dcc.Textarea(
                    id={"type": "notes-input", "index": trade.id_field},
                    style={"width": "100%", "height": 35},
                ),
                dbc.Button("Save Notes", id={"type": "save-notes-btn", "index": trade.id_field}, color="primary",
                           size="sm", className="mt-2", n_clicks=0),
            ], width=7),
            dbc.Col([
                html.Div([
                    html.H3("Strategy")
                ]),
                get_strategy_dropdown(trade),
                html.Div([
                    # add some padding to the top of time frame
                    html.H3("Time Frame", className="mt-4")
                ]),
                get_time_frame_dropdown(trade),
                # html.Span(id={"type": "strategy-output", "index": trade.id_field}, style={"verticalAlign": "middle"}),
            ], width=5),

        ])
    ], className="p-3")


def getTimeFrame(time_frame_id, trade):
    time_frame = TradesDB.getTimeFrameById(time_frame_id)
    if time_frame is None:
        print(f"Warning: Time Frame with ID {time_frame_id} not found for trade {trade.id_field}")
    return time_frame


def getStrat(strat_id, trade):
    strat = TradesDB.getStrategyById(strat_id)
    if strat is None:
        print(f"Warning: Strategy with ID {strat_id} not found for trade {trade.id_field}")
    return strat


def parse_ids(csv_ids):
    if not csv_ids:
        return []
    return [int(s.strip()) for s in csv_ids.split(",") if s.strip().isdigit()]


def get_strategies():
    import sys
    print(sys.executable)

    strats = TradesDB.getStrategiesList()
    stratNames = [obj.name for obj in strats]
    return stratNames


def get_time_frames():
    import sys
    print(sys.executable)

    strats = TradesDB.getTimeFrameList()
    stratNames = [obj.name for obj in strats]
    return stratNames


def get_time_frame_dropdown(trade):
    name_list = []
    for time_frame_id in parse_ids(trade.time_frame):
        time_frame = getTimeFrame(time_frame_id, trade)
        name_list.append(time_frame.name if time_frame else "")
    time_frame_dropdown = html.Div(
        [
            dcc.Dropdown(
                options=get_time_frames(),
                value=name_list,
                multi=True,
                id={"type": "time-frame-dropdown", "index": trade.id_field},
            )
        ]
    )
    return time_frame_dropdown


def get_strategy_dropdown(trade):
    name_list = []
    for strat_id in parse_ids(trade.strategy):
        strat = getStrat(strat_id, trade)
        name_list.append(strat.name if strat else "")
    strategy_dropdown = html.Div(
        [
            dcc.Dropdown(
                options=get_strategies(),
                # placeholder=place_holder,
                value=name_list,
                multi=True,
                id={"type": "strategy-dropdown", "index": trade.id_field}
            )
        ]
    )
    return strategy_dropdown


def _format_notes_with_delete_buttons(notes, trade_id):
    """
    Format trade notes with individual delete buttons that appear on hover.

    Args:
        notes: The notes string from the trade
        trade_id: The trade ID for callback identification

    Returns:
        List of HTML components representing the formatted notes
    """
    if not notes or not notes.strip():
        return ""

    # Split notes into individual lines
    note_lines = [line.strip() for line in notes.split('\n') if line.strip()]

    if not note_lines:
        return ""
    formatted_notes = []

    for index, note_line in enumerate(note_lines):
        # Create a container for each note with hover effect
        note_container = html.Div([
            html.Div([
                html.Span(note_line, style={"flex": "1", "whiteSpace": "pre-wrap"}),
                html.Button(
                    "×",
                    id={"type": "delete-note-btn", "index": trade_id, "note_index": index},
                    className="delete-note-btn",
                    style={
                        "background": "none",
                        "border": "none",
                        "color": "#dc3545",
                        "fontSize": "18px",
                        "fontWeight": "bold",
                        "cursor": "pointer",
                        "padding": "0 5px",
                        "marginLeft": "10px",
                        "opacity": "0",
                        "transition": "opacity 0.2s ease"
                    },
                    n_clicks=0
                )
            ], style={
                "display": "flex",
                "alignItems": "center",
                "padding": "2px 8px",
                "borderRadius": "4px",
                "marginBottom": "2px",
                "transition": "background-color 0.2s ease"
            }, className="note-entry")
        ], style={
            "position": "relative"
        }, className="note-container")

        formatted_notes.append(note_container)

    return formatted_notes


def get_chart_data(trade_list):
    """
    Process trade list to generate chronological profit data for charting.
    
    Args:
        trade_list: List of Trade objects
        
    Returns:
        DataFrame with profit data, total trades count, and win rate
    """
    # Filter out trades with invalid profit values
    valid_trades = [trade for trade in trade_list if
                    trade.profit is not None and
                    trade.created_date is not None and
                    trade.status == TradeStatus.CLOSED]

    if not valid_trades:
        # Return empty DataFrame with correct structure if no valid trades
        empty_df = pd.DataFrame(columns=['profit', 'individual_profit', 'symbol', 'direction'])
        empty_df.index.name = 'created_date'
        return empty_df, 0, 0, []

    # Sort trades chronologically by created_date
    sorted_trades = sorted(valid_trades, key=lambda t: t.created_date)

    # Prepare data for DataFrame
    trade_data = []
    cumulative_profit = 0

    for trade in sorted_trades:
        # Calculate running total
        cumulative_profit += trade.profit

        # Store trade data
        trade_data.append({
            'profit': cumulative_profit,
            'created_date': trade.created_date,
            'individual_profit': trade.profit,
            'symbol': trade.symbol,
            'direction': trade.direction.value if hasattr(trade.direction, 'value') else str(trade.direction)
        })

    # Create DataFrame and set index
    profit_frame = pd.DataFrame(trade_data)
    profit_frame.set_index('created_date', inplace=True)

    # Calculate statistics
    total_trades = len(profit_frame)

    if total_trades > 0:
        win_count = sum(1 for trade in sorted_trades if trade.profit > 0)
        win_rate = round((win_count / total_trades) * 100)
    else:
        win_rate = 0

    return profit_frame, total_trades, win_rate, sorted_trades


# ================================
# Dash Layout
# ================================

def getAccordionItems(trade_list):
    if trade_list is None:
        return None, None
    items = []
    for t in trade_list:
        # Determine row color based on trade status and profit
        if t.status.name == "OPEN":
            # Blue tint for open trades
            row_color = "rgba(0, 123, 255, 0.1)"
        else:
            # Color based on profit/loss for closed trades
            if t.profit > 0:
                # Green shades for profit (darker green = more profit)
                profit_intensity = min(float(abs(t.profit)) / 1000, 1.0)  # Normalize to 0-1
                row_color = f"rgba(40, 167, 69, {0.1 + profit_intensity * 0.3})"
            elif t.profit < 0:
                # Red shades for loss (darker red = more loss)
                loss_intensity = min(float(abs(t.profit)) / 1000, 1.0)  # Normalize to 0-1
                row_color = f"rgba(220, 53, 69, {0.1 + loss_intensity * 0.3})"
            else:
                # Neutral gray for breakeven
                row_color = "rgba(108, 117, 125, 0.1)"

        row = dbc.Row(
            [
                dbc.Col(html.Div(t.status.name, id={"type": "status-cell", "index": t.id_field}), width=1),
                dbc.Col([
                    html.Div([
                        get_exchange_icon(t.exchange),
                        html.Span(t.symbol, style={"marginLeft": "5px"}),
                        html.Span(" 📝", title="Manual Trade", style={"color": "#6f42c1", "marginLeft": "5px"})
                        if t.exchange_trade_id and t.exchange_trade_id.startswith("MANUAL") else None
                    ], style={"display": "flex", "alignItems": "center"})
                ], width=2),  # Trading Symbol (e.g., BTC/USD)
                dbc.Col(html.Div(helper.format_decimal(t.tradeQty), id={"type": "quantity-cell", "index": t.id_field}), width=1),  # Total Quantity
                dbc.Col(html.Div(helper.formatDate(t.closed_date, date_format='%m/%d/%y') if t.closed_date else "Open",
                                 id={"type": "time-cell", "index": t.id_field}), width=1), # Time Closed
                dbc.Col(html.Div(t.getTradeDurationString(), id={"type": "duration-cell", "index": t.id_field}), width=2),  # Trade Duration
                dbc.Col(html.Div(t.direction.value), width=1),  # Direction (Long/Short)
                dbc.Col(html.Div("$" + t.notionalString(), id={"type": "notional-cell", "index": t.id_field}), width=1), # Notional Value
                dbc.Col(html.Div("$" + t.profitString(), id={"type": "profit-cell", "index": t.id_field}), width=1), # Profit/Loss
                html.Div(id={"type": "profit-flash-trigger", "index": t.id_field}, style={"display": "none"}),
                html.Div(id={"type": "profit-reset-timer", "index": t.id_field}, style={"display": "none"}),
                dbc.Col(html.Div("$" + t.feesString(), id={"type": "fees-cell", "index": t.id_field}), width=1), # Trading Fees
                dbc.Col(
                    html.Span(
                        "▶",
                        id={"type": "custom-caret", "index": t.id_field},
                        style={
                            "fontSize": "16px",
                            "transition": "transform 0.2s ease",
                            "display": "inline-block",
                            "transform": "rotate(0deg)"
                        },
                        className="custom-caret"
                    ),
                    width=1
                )
            ],
            align="center",
            className="m-0 p-0",
        )
        items.append(
            dbc.AccordionItem(
                children=html.Div(id={"type": "accord-content", "index": t.id_field}),
                item_id=f"item-{t.id_field}",
                className="m-0 p-0",
                title=html.Div(
                    [row, html.Div(id={"type": "accord-div", "index": t.id_field})],
                    style={"width": "100%", "cursor": "pointer", "backgroundColor": row_color},
                    className="m-0 p-2 fs-7 fw-light"
                )
            )
        )
    return items, trade_list


# ================================
# Dash Callbacks
# ================================

def get_callbacks(app):
    # Add clientside callback to scroll to active accordion item
    app.clientside_callback(
        """
        function(active_item) {
            console.log("Active item:", active_item);
            if (active_item) {
                // Small delay to ensure accordion item is expanded
                setTimeout(function() {
                    // 1) extract the "22"
                    const idx = parseInt(active_item.split("-")[1], 10);
                    
                    // 2) build your payload
                    const payload = {
                      index: idx,
                      type: "accord-div"
                    };
                    // if you really need it as a JSON string:
                    const payloadStr = JSON.stringify(payload);
                    console.log(payloadStr);
                    const element = document.getElementById(payloadStr);
                    console.log("Element:", element);
                    if (element) {
                        element.scrollIntoView({behavior: 'smooth', block: 'center'});
                        // Highlight the element briefly
                        element.style.transition = 'background-color 0.5s';
                        element.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                        setTimeout(function() {
                            element.style.backgroundColor = '';
                        }, 1000);
                    }
                }, 300); // Delay to ensure accordion animation completes
            }
            return window.dash_clientside.no_update;
        }
        """,
        Output("accord", "data-scroll"),
        Input("accord", "active_item"),
        prevent_initial_call=True
    )

    # Add clientside callback for "Return to Chart" button
    app.clientside_callback(
        """
        function(n_clicks) {
            if (n_clicks > 0) {
                // Scroll to the nav-bar
                const navElement = document.getElementById("nav-bar");
                console.log(navElement);
                if (navElement) {
                    navElement.scrollIntoView({behavior: 'smooth', block: 'start'});
                }
                return null;
            }
            return window.dash_clientside.no_update;
        }
        """,
        Output("accord", "active_item", allow_duplicate=True),
        Input("return-to-top-btn", "n_clicks"),
        prevent_initial_call=True
    )

    @app.callback(
        [Output("pnl_graph", 'figure'),
         Output("accord", 'children'),
         Output("infoRow", 'children'),
         Output("accord", "active_item", allow_duplicate=True)],  # Add output for accordion active item
        [Input("trade-search", "n_clicks"),
         Input("pnl_graph", "clickData")],  # Add input for graph click data
        ## Saved Search Bar Data ##
        [State("selected-dates", "data"),
         State({"type": "crypto-checklist", "index": dash.ALL}, "value")],
        prevent_initial_call="initial_duplicate"
    )
    def trade_search(n_clicks, click_data, selected_dates, selected_symbols):
        ctx = dash.callback_context
        triggered_id = ctx.triggered_id

        # Handle chart click to navigate to trade
        if triggered_id == "pnl_graph" and click_data:
            if click_data and click_data["points"] and len(click_data["points"]) > 0:
                point = click_data["points"][0]
                if "customdata" in point and len(point["customdata"]) >= 4:
                    trade_id = point["customdata"][3]
                    if trade_id:
                        # Return no_update for everything except active_item
                        return dash.no_update, dash.no_update, dash.no_update, f"item-{trade_id}"
            return dash.no_update, dash.no_update, dash.no_update, dash.no_update

        # Original search functionality
        start_date, end_date = (
            datetime.strptime(date_str, "%Y-%m-%d").date() if date_str else None
            for date_str in (selected_dates or [None, None])
        )

        if start_date and end_date is not None:
            start_date = start_date.strftime("%Y-%m-%d")
            end_date = end_date.strftime("%Y-%m-%d")
            trade_list = TradesDB.get_trades(startDate=start_date,
                                             endDate=end_date,
                                             symbol_list=selected_symbols,
                                             include_open_trades=True)
            if len(trade_list) > 0:
                items, trade_list = getAccordionItems(trade_list)
                # Closed orders only from profit time series
                profit_frame, total_trades, win_rate, sorted_trades = get_chart_data(trade_list)

                # Create figure using go.Figure instead of px.area
                fig = go.Figure()

                # Add area trace for cumulative profit
                fig.add_trace(
                    go.Scatter(
                        x=profit_frame.index,
                        y=profit_frame["profit"],  # This is the cumulative profit for the line
                        fill='tozeroy',
                        fillcolor="rgba(0, 123, 255, 0.2)",
                        line=dict(color="rgba(0, 123, 255, 1)", width=2),
                        name="Cumulative P&L",
                        mode="lines+markers",  # Add markers for better click targets
                        marker=dict(
                            size=8,
                            opacity=0.6,
                            line=dict(width=1, color="white")
                        ),
                        customdata=np.column_stack((
                            profit_frame["individual_profit"],
                            profit_frame["symbol"],
                            profit_frame["direction"],
                            # Add trade ID for linking to accordion
                            [trade.id_field for trade in sorted_trades] if sorted_trades else []
                        )),
                        hovertemplate=
                        "<b>Date</b>: %{x|%b %d, %Y}<br>" +
                        "<b>Cumulative P&L</b>: $%{y:.2f}<br>" +
                        "<b>Trade P&L</b>: $%{customdata[0]:.2f}<br>" +
                        "<b>Symbol</b>: %{customdata[1]}<br>" +
                        "<b>Direction</b>: %{customdata[2]}<br>" +
                        "<i>Click to view trade details</i>" +
                        "<extra></extra>"
                    )
                )

                # Update layout
                fig.update_layout(
                    # title="Cumulative Profit/Loss Over Time",
                    # xaxis_title="Date",
                    # yaxis_title="Profit/Loss ($)",
                    yaxis=dict(
                        tickprefix="$",
                        gridcolor="rgba(0,0,0,0.1)",
                        zeroline=True,
                        zerolinecolor="rgba(0,0,0,0.2)",
                        zerolinewidth=1,
                    ),
                    xaxis=dict(
                        gridcolor="rgba(0,0,0,0.1)",
                    ),
                    hovermode="closest",
                    hoverdistance=100,
                    hoverlabel=dict(
                        bgcolor="white",
                        font_size=12,
                        font_family="Arial",
                        bordercolor="gray",
                        align="left"
                    ),
                    clickmode="event",  # Enable click events
                    legend=dict(
                        title="",
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1
                    ),
                    margin=dict(l=10, r=10, t=40, b=10),
                    height=500,
                    plot_bgcolor="white",
                )

                # Add colored rectangles for profit/loss areas
                if len(profit_frame) > 0:
                    min_profit = profit_frame["profit"].min()
                    max_profit = profit_frame["profit"].max()

                    fig.add_hrect(
                        y0=0, y1=min_profit - 10 if min_profit < 0 else -10,
                        fillcolor="rgba(220, 53, 69, 0.1)",
                        line_width=0,
                        layer="below"
                    )

                    fig.add_hrect(
                        y0=0, y1=max_profit + 10 if max_profit > 0 else 10,
                        fillcolor="rgba(40, 167, 69, 0.1)",
                        line_width=0,
                        layer="below"
                    )

                wins = sum(1 for trade in trade_list if trade.profit > 0 and trade.status == TradeStatus.CLOSED)
                losses = sum(1 for trade in trade_list if trade.profit < 0 and trade.status == TradeStatus.CLOSED)

                closed_trades_count = sum(1 for trade in trade_list if
                                          trade.profit is not None and trade.status == TradeStatus.CLOSED)

                average_profit = (sum(trade.profit for trade in trade_list if
                                      trade.profit is not None and trade.status == TradeStatus.CLOSED) /
                                  closed_trades_count) if closed_trades_count > 0 else 0

                closed_duration_count = sum(1 for trade in trade_list if
                                            trade.duration is not None and trade.status == TradeStatus.CLOSED)

                average_duration = (sum(trade.duration for trade in trade_list if
                                        trade.duration is not None and trade.status == TradeStatus.CLOSED) /
                                    closed_duration_count) if closed_duration_count > 0 else 0

                long_trades = sum(1 for trade in trade_list if trade.direction == TradeDirection.LONG)
                short_trades = sum(1 for trade in trade_list if trade.direction == TradeDirection.SHORT)
                long_percentage = round((long_trades / len(trade_list)) * 100)
                short_percentage = round((short_trades / len(trade_list)) * 100)

                infoRow = (dbc.Col(get_total_trades_card(len(trade_list), wins, losses), width=3),
                           dbc.Col(get_win_rate_card(win_rate, len(trade_list), wins, losses), width=3),
                           dbc.Col(get_average_trade_time_card(average_duration), width=3),
                           dbc.Col(
                               get_long_short_ratio_card(long_percentage, short_percentage, long_trades, short_trades),
                               width=3))

                return (fig,
                        items,
                        infoRow,
                        dash.no_update
                        )
            else:
                infoRow = (dbc.Col(get_total_trades_card(0, 0, 0), width=3),
                           dbc.Col(get_win_rate_card(0, len(trade_list), 0, 0), width=3),
                           dbc.Col(get_average_trade_time_card(0), width=3),
                           dbc.Col(get_long_short_ratio_card(0, 0, 0, 0), width=3))
                return dash.no_update, [], infoRow, dash.no_update
        return None

    # @app.callback(
    #     Output("accord", "active_item"),
    #     Input("return-to-top-btn", "n_clicks"),
    #     prevent_initial_call=True
    # )
    # def close_row(n_clicks):
    #     if n_clicks == 0:
    #         return dash.no_update
    #     return None

    @app.callback(
        Input("accord", "active_item"),
        prevent_initial_call=True
    )
    def active_item(active):
        if active is None:
            return
        trade_id = int(active.split("-")[1])
        set_props({'type': 'accord-div', 'index': trade_id}, {"n_clicks": "7"})

    # We use set_props here to open row and add/update row contents to reduce overhead and extra callbacks
    # @app.callback(
    #     Input("accord", "active_item"),
    #     prevent_initial_call=True
    #
    # Original way of using Output for rowOpened
    @app.callback(
        Output({'type': 'accord-content', 'index': MATCH}, 'children'),
        Input({'type': 'accord-div', 'index': MATCH}, 'n_clicks'),
        State({'type': 'accord-content', 'index': MATCH}, 'children'),
        prevent_initial_call=True
    )
    def row_opened(n_clicks, children):
        # if children is not None:
        #     return dash.no_update, dash.no_update

        trade_id = json.loads(dash.ctx.triggered[0]["prop_id"].split(".")[0])["index"]
        trade = TradesDB.get_trade_by_id(trade_id=trade_id)

        # TRADE GRAPH SECTION
        fig = getTradeGraph(trade)
        fig.update_layout(
            height=150,
            width=300,
            margin=dict(l=0, r=0, t=0, b=0)
        )

        trade_graph = dcc.Graph(id={"type": "graph", "index": 1},
                                figure=fig,
                                config={'scrollZoom': True,
                                        'modeBarButtonsToRemove': ['lasso', 'select', 'zoom']}
                                )

        row_data = dbc.Container(
            [
                dbc.Row(
                    [
                        dbc.Col([
                            trade_graph,
                            html.A("View TV Chart", href="#" or trade.chartLink,
                                   target="_blank", className="btn btn-outline-primary btn-sm mt-2")
                        ], width=4),
                        dbc.Col(format_trade_details(trade), width=8)],
                ),
                # Only show Add Order button for manually created trades
                dbc.Row(
                    [
                        dbc.Col([
                            dbc.Button([
                                html.I(className="bi bi-plus-circle me-2"),
                                "Add Order"
                            ],
                                id={"type": "add-order-btn", "index": trade.id_field},
                                color="success",
                                size="sm",
                                className="mb-3")
                        ], width=12)
                    ]
                ) if trade.exchange_trade_id and trade.exchange_trade_id.startswith("MANUAL") else html.Div(),
                dbc.Row(
                    [
                        dbc.Col(generate_trades_table(trade.trade_orders), width=12)
                    ], className="padding-top padding-bottom"),
                dbc.Row(
                    [
                        get_return_to_chart_button()
                    ]
                )
            ], fluid=True
        )

        return row_data

    # @app.callback(
    #     Output("previous-active-item", "data"),
    #     Input("accord", "active_item"),
    #     State("previous-active-item", "data"),
    #     prevent_initial_call=True
    # )
    # def update_all_carets(active_item, previous_active_item):
    #     # Reset the previous caret if it exists and is different from current
    #     if previous_active_item and previous_active_item != active_item:
    #         previous_trade_id = int(previous_active_item.split("-")[1])
    #         set_props(
    #             {'type': 'custom-caret', 'index': previous_trade_id},
    #             {
    #                 "style": {
    #                     "fontSize": "16px",
    #                     "transition": "transform 0.2s ease",
    #                     "display": "inline-block",
    #                     "transform": "rotate(0deg)"
    #                 }
    #             }
    #         )
    #
    #     # Set the current active caret if it exists
    #     if active_item:
    #         active_trade_id = int(active_item.split("-")[1])
    #         set_props(
    #             {'type': 'custom-caret', 'index': active_trade_id},
    #             {
    #                 "style": {
    #                     "fontSize": "16px",
    #                     "transition": "transform 0.2s ease",
    #                     "display": "inline-block",
    #                     "transform": "rotate(90deg)"
    #                 }
    #             }
    #         )
    #
    #     # Return the current active_item to store as the new previous
    #     return active_item

    def get_return_to_chart_button():
        return dbc.Col(
            html.Div(
                dbc.Button(
                    [
                        html.I(className="bi bi-arrow-up me-2"),  # Bootstrap icon for up arrow
                        "Return to Chart"
                    ],
                    id="return-to-top-btn",
                    color="secondary",
                    outline=True,
                    size="sm",
                    className="float-end mb-3",
                    n_clicks=0
                ),
                className="text-end"
            ),
            width=12
        )

    @app.callback(
        Output("accord", "children", allow_duplicate=True),
        Input("price-update-interval", "n_intervals"),
        State("selected-dates", "data"),
        State({"type": "crypto-checklist", "index": dash.ALL}, "value"),
        prevent_initial_call=True
    )
    def update_trades_realtime(n_intervals, selected_dates, selected_symbols):
        """Update trade values for open trades every 30 seconds without reloading accordion"""
        print(f"\nPrice update callback triggered! n_intervals: {n_intervals}")

        # Import the status dictionaries from background_tasks
        from dash_app.widgets.background_tasks import coinbase_status, sierra_status, bybit_status, blofin_status

        # Check if any import is currently running
        if (coinbase_status["running"] or
                sierra_status["running"] or
                bybit_status["running"] or
                blofin_status["running"]):
            print("Skipping update_trades_realtime - import process is running")
            return dash.no_update

        start_date, end_date = (
            datetime.strptime(date_str, "%Y-%m-%d").date() if date_str else None
            for date_str in (selected_dates or [None, None])
        )

        if start_date and end_date:
            start_date = start_date.strftime("%Y-%m-%d")
            end_date = end_date.strftime("%Y-%m-%d")
            trade_list = TradesDB.get_trades(startDate=start_date,
                                             endDate=end_date,
                                             symbol_list=selected_symbols,
                                             include_open_trades=True)

            # Update only open trades and use set_props to update display
            for trade in trade_list:
                if trade.status == TradeStatus.OPEN:
                    old_profit = trade.profit
                    trade.calculate_profit()  # This gets the current market price
                    trade.update_trade_details(save=True)  # Update duration and other fields

                    # Update all dynamic fields
                    set_props(
                        {'type': 'status-cell', 'index': trade.id_field},
                        {"children": trade.status.name}
                    )
                    set_props(
                        {'type': 'quantity-cell', 'index': trade.id_field},
                        {"children": helper.format_decimal(trade.tradeQty)}
                    )
                    set_props(
                        {'type': 'time-cell', 'index': trade.id_field},
                        {"children": helper.formatDate(trade.closed_date,
                                                       date_format='%m/%d/%y') if trade.closed_date else "Open"}
                    )
                    set_props(
                        {'type': 'duration-cell', 'index': trade.id_field},
                        {"children": trade.getTradeDurationString()}
                    )
                    set_props(
                        {'type': 'notional-cell', 'index': trade.id_field},
                        {"children": "$" + trade.notionalString()}
                    )
                    set_props(
                        {'type': 'profit-cell', 'index': trade.id_field},
                        {"children": "$" + trade.profitString()}
                    )
                    set_props(
                        {'type': 'fees-cell', 'index': trade.id_field},
                        {"children": "$" + trade.feesString()}
                    )

                    # Determine flash effect based on profit change
                    if old_profit != trade.profit:
                        profit_increased = trade.profit > old_profit
                        flash_color = "rgba(40, 167, 69, 0.3)" if profit_increased else "rgba(220, 53, 69, 0.3)"
                        log_message = f"Updated trade {trade.id_field}: ${trade.profitString()} ({'↑' if profit_increased else '↓'})"
                    else:
                        flash_color = "rgba(108, 117, 125, 0.2)"  # Light gray for no change
                        log_message = f"Refreshed trade {trade.id_field}: ${trade.profitString()} (no change)"

                    # Apply flash effect - force the style update
                    set_props(
                        {'type': 'profit-cell', 'index': trade.id_field},
                        {
                            "children": "$" + trade.profitString(),
                            "style": {
                                "backgroundColor": flash_color,
                                # "fontWeight": "bold" if old_profit == trade.profit else "normal",
                                "transition": "all 0.3s ease"
                            }
                        }
                    )

                    # Trigger flash removal with timestamp to ensure it always changes
                    import time
                    set_props(
                        {'type': 'profit-flash-trigger', 'index': trade.id_field},
                        {"data-flash": f"{trade.id_field}_{int(time.time() * 1000)}"}  # Add timestamp
                    )

                    print(log_message)

        return dash.no_update

    @app.callback(
        Output({'type': 'profit-cell', 'index': MATCH}, 'style'),
        Input({'type': 'profit-flash-trigger', 'index': MATCH}, 'data-flash'),
        prevent_initial_call=True
    )
    def reset_profit_cell_style(flash_trigger):
        if flash_trigger:
            # Return the flash style immediately, then use a separate interval to reset
            trade_index = int(flash_trigger.split('_')[0])

            # Schedule a reset using dcc.Store to trigger after delay
            import time
            reset_time = time.time() + 1  # Reset after 1 second

            # Store the reset time for this trade
            set_props(
                {'type': 'profit-reset-timer', 'index': trade_index},
                {"data": reset_time}
            )

            return dash.no_update

        return dash.no_update

    # Add a callback to handle the reset timing
    @app.callback(
        Output({'type': 'profit-cell', 'index': MATCH}, 'style', allow_duplicate=True),
        Input({'type': 'profit-reset-timer', 'index': MATCH}, 'data'),
        prevent_initial_call=True
    )
    def handle_profit_reset(reset_time):
        if reset_time:
            import time
            current_time = time.time()
            print(
                f"[DEBUG] Reset check - Current: {current_time}, Reset: {reset_time}, Diff: {current_time - reset_time}")

            # if current_time >= reset_time:
            # Extract trade_index from the callback context
            trade_index = dash.ctx.triggered_id['index']
            print(f"[DEBUG] Resetting style for trade {trade_index}")

            set_props(
                {'type': 'profit-cell', 'index': trade_index},
                {
                    "style": {
                        "backgroundColor": '',
                        "transition": "all 0.3s ease"
                    }
                }
            )
            return dash.no_update
            # else:
            #     print(f"[DEBUG] Not ready to reset yet, waiting {reset_time - current_time:.2f} more seconds")
        return dash.no_update

    # app.clientside_callback(
    #     """
    #     function(flash_trigger) {
    #         if (flash_trigger) {
    #             // Extract trade index from the timestamp format
    #             const tradeIndex = flash_trigger.split('_')[0];
    #
    #             // Find the specific profit cell for this trade
    #             const profitCellId = JSON.stringify({index: parseInt(tradeIndex), type: "profit-cell"});
    #             const profitCell = document.getElementById(profitCellId);
    #
    #             if (profitCell) {
    #                 // Completely reset the style to allow new updates
    #                 // profitCell.style.backgroundColor = 'Yellow';
    #             }
    #         }
    #         return window.dash_clientside.no_update;
    #     }
    #     """,
    #     Output({'type': 'profit-flash-trigger', 'index': MATCH}, 'data-flash'),
    #     Input({'type': 'profit-flash-trigger', 'index': MATCH}, 'data-flash'),
    #     prevent_initial_call=True
    # )

    @app.callback(
        Output({"type": "trade-notes", "index": MATCH}, "children"),
        Output({"type": "notes-input", "index": MATCH}, "value"),
        Input({"type": "save-notes-btn", "index": MATCH}, "n_clicks"),
        State({"type": "notes-input", "index": MATCH}, "value"),
        prevent_initial_call=True
    )
    def save_notes_click(n_clicks, value):
        trade_id = json.loads(dash.ctx.triggered[0]["prop_id"].split(".")[0])["index"]
        notes = TradesDB.save_trade_note(trade_id, value)
        return _format_notes_with_delete_buttons(notes, trade_id), ""

    @app.callback(
        Output({"type": "trade-notes", "index": MATCH}, "children", allow_duplicate=True),
        Input({"type": "delete-note-btn", "index": MATCH, "note_index": ALL}, "n_clicks"),
        prevent_initial_call=True
    )
    def delete_note_click(n_clicks_list):
        """
        Handle the deletion of individual trade notes.

        Args:
            n_clicks_list: List of n_clicks values for all delete buttons
        """
        # Check if any button was actually clicked
        if not n_clicks_list or not any(n_clicks_list) or not dash.ctx.triggered:
            return dash.no_update

        # Get the triggered component info
        triggered_prop_id = dash.ctx.triggered[0]["prop_id"]
        if not triggered_prop_id or triggered_prop_id == ".":
            return dash.no_update

        try:
            triggered_component = json.loads(triggered_prop_id.split(".")[0])
            trade_id = triggered_component["index"]
            note_index = triggered_component["note_index"]

            # Validate inputs
            if trade_id is None or note_index is None:
                return dash.no_update

            # Delete the note from the database
            updated_notes = TradesDB.delete_trade_note(trade_id, note_index)

            # Return the updated formatted notes
            return _format_notes_with_delete_buttons(updated_notes, trade_id)
        except Exception as e:
            # Handle any other errors gracefully
            print(f"❌ Error deleting note: {str(e)}", file=sys.stderr)
            traceback.print_exc()
            return dash.no_update

    # @app.callback(
    #     Output({"type": "strategy-dropdown", "index": MATCH}, "options"),
    #     Input({"type": "strategy-dropdown", "index": MATCH}, "search_value"),
    # )
    # def search_strategies_dropdown(search_value):
    #     if search_value is None or search_value == '' or search_value == ' ':
    #         return dash.no_update
    #     stratList = []
    #     if search_value in stratList:
    #         print("Already have strat")

    @app.callback(
        # Output({"type": "strategy-dropdown", "index": ALL}, "value", allow_duplicate=True),
        Input({"type": "strategy-dropdown", "index": ALL}, "value"),
        suppress_callback_exceptions=True, prevent_initial_call=True)
    def save_strategy(strat_names):
        if not dash.ctx.triggered:
            return None

        # Get the triggered component information
        triggered = dash.ctx.triggered[0]
        triggered_id = json.loads(triggered["prop_id"].split(".")[0])
        trade_id = triggered_id["index"]

        # Find the index of the triggered dropdown in the ALL pattern match
        # to get the correct values from strat_names
        triggered_idx = None
        for i, input_item in enumerate(dash.callback_context.inputs_list[0]):
            if input_item['id']['index'] == trade_id:
                triggered_idx = i
                break

        # Use the values from the specific dropdown that triggered the callback
        if triggered_idx is not None:
            selected_values = strat_names[triggered_idx]
            TradesDB.save_trade_strategy(trade_id=trade_id, strat_names=selected_values)

        return None

    @app.callback(
        # Output({"type": "time-frame-dropdown", "index": ALL}, "value", allow_duplicate=True),
        Input({"type": "time-frame-dropdown", "index": ALL}, "value"),
        suppress_callback_exceptions=True, prevent_initial_call=True)
    def save_time_frame(time_frame_value):
        if not dash.ctx.triggered:
            return None

        # Get the triggered component information
        triggered = dash.ctx.triggered[0]
        triggered_id = json.loads(triggered["prop_id"].split(".")[0])
        trade_id = triggered_id["index"]

        # Find the index of the triggered dropdown in the ALL pattern match
        # to get the correct values from time_frame_value
        triggered_idx = None
        for i, input_item in enumerate(dash.callback_context.inputs_list[0]):
            if input_item['id']['index'] == trade_id:
                triggered_idx = i
                break

        # Use the values from the specific dropdown that triggered the callback
        if triggered_idx is not None:
            selected_values = time_frame_value[triggered_idx]
            TradesDB.save_trade_time_frame(trade_id=trade_id, time_frames=selected_values)

        return None

    ###########################
    # Helper Functions
    ###########################

    def getTradeGraph(trade=None):
        """
        Generate a candlestick chart for a trade, focusing on the trade's active period.
        
        Args:
            trade: Trade object containing created_date, closed_date, avgOpenPrice, and avgClosePrice
            
        Returns:
            Plotly Figure object with candlestick chart and trade markers
        """
        # TODO: Replace with real market data from TradingView or crypto exchange API
        # For now, using sample AAPL data but adjusting the view
        df = pd.read_csv('https://raw.githubusercontent.com/plotly/datasets/master/finance-charts-apple.csv')

        # Create base candlestick chart
        fig = go.Figure(go.Candlestick(
            x=df['Date'],
            open=df['AAPL.Open'],
            high=df['AAPL.High'],
            low=df['AAPL.Low'],
            close=df['AAPL.Close'],
        ))

        # If trade data is provided, enhance the chart with trade-specific information
        if trade and trade.created_date:
            # Convert trade dates to string format matching the sample data
            # In real implementation, we would use actual timestamps

            # For demo purposes, use a subset of the data to simulate the trade period
            # In a real implementation; we would filter by actual trade dates
            if len(df) > 10:
                start_idx = max(0, len(df) // 2 - 5)
                end_idx = min(len(df), len(df) // 2 + 5)

                # Set x-axis range to focus on trade period
                fig.update_xaxes(range=[df['Date'][start_idx], df['Date'][end_idx - 1]])

                # Add entry point marker
                entry_price = trade.avgOpenPrice if trade.avgOpenPrice else df['AAPL.Close'][start_idx]
                fig.add_trace(go.Scatter(
                    x=[df['Date'][start_idx]],
                    y=[entry_price],
                    mode='markers',
                    marker=dict(color='green', size=10, symbol='triangle-up'),
                    name='Entry'
                ))

                # Add exit point marker for closed trades
                if trade.status == TradeStatus.CLOSED and trade.avgClosePrice:
                    exit_price = trade.avgClosePrice
                    fig.add_trace(go.Scatter(
                        x=[df['Date'][end_idx - 1]],
                        y=[exit_price],
                        mode='markers',
                        marker=dict(color='red', size=10, symbol='triangle-down'),
                        name='Exit'
                    ))

        # Configure chart layout
        fig.update_layout(
            dragmode='drawrect',
            xaxis_rangeslider_visible=False,
            height=150,
            width=300,
            margin=dict(l=0, r=0, t=0, b=0),
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        return fig

    def generate_trades_table(orders):
        return html.Table(
            # Table with headers and rows
            children=[
                # Table Header
                html.Thead(
                    html.Tr([
                        html.Th("Created Date"),
                        html.Th("Filled Date"),
                        html.Th("Order Type"),
                        html.Th("Buy/Sell"),
                        html.Th("Quantity"),
                        html.Th("Price"),
                        html.Th("Order Status"),
                        html.Th("Source")
                    ])
                ),
                # Table Body
                html.Tbody([
                    html.Tr(
                        [
                            html.Td(helper.formatDate(order.created_date)),
                            html.Td(helper.formatDate(order.filled_date)),
                            html.Td(order.orderType),
                            html.Td(str(order.buySell.value)),
                            html.Td(str(order.quantity)),
                            html.Td(f"${order.price:.2f}" if order.price is not None else "N/A"),  # Format price
                            html.Td(order.orderStatus),
                            html.Td([
                                html.Span("Manual",
                                          className="badge bg-info text-dark") if order.order_id and order.order_id.startswith(
                                    "MANUAL")
                                else html.Span("Exchange", className="badge bg-secondary")
                            ]),
                        ],
                        style={
                            # Enhanced color logic: Purple border for manual orders, original colors for others
                            "backgroundColor": "#fff3cd" if not order.filled else "#d4edda" if order.buySell == BuySell.BUY else "#f8d7da",
                            "color": "#856404" if not order.filled else "#155724" if order.buySell == BuySell.BUY else "#721c24"
                        }
                    )
                    for order in orders
                ])
            ],
            style={"border": "1px solid black", "width": "100%", "borderCollapse": "collapse"},
        )

# @callback(
#     Output(component_id={'type': 'alert', 'index': MATCH}, component_property='children'),
#     Input(component_id={'type': 'graph', 'index': MATCH}, component_property='relayoutData'))
# def userInput(col_chosen):
#     print(col_chosen)
#     if col_chosen is not None:
#         x0 = str(col_chosen["shapes"][-1]["x0"])
#         x1 = str(col_chosen["shapes"][-1]["x1"])
#         y0 = str(col_chosen["shapes"][-1]["y0"])
#         y1 = str(col_chosen["shapes"][-1]["y1"])
#         return "Coordinates Are: " + x0 + " / " + y0 + "  -  " + x1 + " / " + y1
#     else:
#         return "None"


# @callback(
#     Output(component_id='graph', component_property='figure'),
#     Input(component_id='toggle-rangeslider', component_property='value'),
#     State(component_id='graph', component_property='figure'))
# def update_chart(value, figure):
#     if figure is None:
#         df = pd.read_csv('https://raw.githubusercontent.com/plotly/datasets/master/finance-charts-apple.csv')
#         figure = go.Figure(go.Candlestick(
#             x=df['Date'],
#             open=df['AAPL.Open'],
#             high=df['AAPL.High'],
#             low=df['AAPL.Low'],
#             close=df['AAPL.Close'],
#         ))
#         figure.update_layout(dragmode='drawrect',
#                              xaxis_rangeslider_visible=False)
#     else:
#         figure['layout']['xaxis']['rangeslider']['visible'] = False in value
#     return figure
