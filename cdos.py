import sys
import traceback
from datetime import datetime, timezone, timedelta

from dash import html

import helper
import trades_db
from models.coinbase_order import CoinbaseOrder
from models.order import Order
from models.trade import Trade, Exchange, TradeDirection
from models.trade import TradeStatus
from services import coinbase_advanced
from services.bybit import Bybit
from trades_db import TradesDB


# want a vibe code challenge? bottom is 10 day rolling normalization window of spot cvd and (perp cvd - spot cvd)
# with an open interest multiplier. spot above = buy, spot below = sell. gl.

# Marketing Angle Opportunities
#
# "The first truly private trading journal"
# "Your trading strategies stay yours - provably"
# "Built for traders who left CeFi for DeFi"
# "Journal your trades without journaling to us"


def get_new_orders(username, status, exchange, category: str = "linear", settleCoin: str = None):
    # Get the set of already processed order IDs
    status["status"] = "Getting Order history.."
    orders = []
    current_open_positions = []
    first_import = False
    match exchange:
        case Exchange.COINBASE:
            current_open_positions, orders, first_import = coinbase_advanced.get_coinbase_orders()
            status["status"] = f"Got {len(orders)} Orders and {len(current_open_positions)} Open Positions"

            # Process the orders with the current open positions
            count = process_coinbase_orders(username, orders, status, current_open_positions, first_import)
        case Exchange.BYBIT:
            current_open_positions, orders, first_import = Bybit.get_bybit_orders(category=category,
                                                                                  settleCoin=settleCoin,
                                                                                  username=username)
            status["status"] = f"Got {len(orders)} Orders and {len(current_open_positions)} Open Positions"

            # Process the orders with the current open positions
            count = process_orders(username, orders, status, current_open_positions, first_import)

    # Update the status with information about the last import
    last_import_date = TradesDB.get_coinbase_last_order_time() if exchange == Exchange.COINBASE else TradesDB.get_bybit_last_order_time()
    if last_import_date:
        duration_ms = helper.calculate_duration_seconds(helper.mSToDate(last_import_date), datetime.now(timezone.utc))
        duration_since_last_import = helper.format_duration(duration_ms)
        status["status"] = [
            "Done! Processed ",
            html.Strong(count),
            " Orders!",
            html.Br(),
            "Last Order imported was ",
            html.Strong(duration_since_last_import),
            " ago ✅"
        ]
    else:
        status["status"] = f"Error Processing Orders, processed {html.Strong(count)} Orders"


def process_coinbase_orders(username, orders, status, current_open_positions, first_import):
    # orders = [order for order in orders if normalize_symbol(order.symbol) == "XLM-29AUG25-CDE"]
    trades_to_save = []
    trades_to_update = []
    open_trades_by_symbol_direction = {}

    # Prime the dictionary with current open positions
    for trade in current_open_positions:
        if trade.status == TradeStatus.OPEN:
            norm_symbol = normalize_symbol(trade.symbol)
            open_trades_by_symbol_direction[(norm_symbol, trade.direction)] = trade

    # Pre-normalize all order symbols to avoid repeated normalization
    normalized_order_symbols = {order: normalize_symbol(order.symbol) for order in orders}

    count = 0
    for count, order in enumerate(orders, start=1):
        status["status"] = f"Processing Trade #{count}"
        order.printInfo()

        # Fast lookup using pre-computed key
        norm_symbol = normalized_order_symbols[order]

        # Find matching open trade using dictionary lookup instead of linear search
        open_trade = None
        for direction in [TradeDirection.LONG, TradeDirection.SHORT]:
            key = (norm_symbol, direction)
            if key in open_trades_by_symbol_direction:
                open_trade = open_trades_by_symbol_direction[key]
                break

        if open_trade:
            trades_to_update.append(open_trade)
            if order.filled:
                # Extra quantity happens for Coinbase only when a user sells 3 with 1 open (-2) which results an
                extra_quantity = open_trade.update_with_order(order, False)

                if open_trade.status == TradeStatus.CLOSED:
                    # Unfilled orders placed outside the trade fill's timeframe need to be accounted for
                    add_unfilled_orders(orders, open_trade)
                    open_trade.calculateRisk()  # Calculate risk only once when needed
                    del open_trades_by_symbol_direction[(norm_symbol, open_trade.direction)]

                # Now handle extra QTY if needed (They open new positions automatically in some cases)
                if extra_quantity < 0:
                    if open_trade.exchange == Exchange.COINBASE:
                        # Trade was closed by order overlap, I.E. open_trade is Long 2 and user Sells 5,
                        # which closes the order and opens a short (Coinbase only)
                        open_trade.close_overflow_trade()
                        add_unfilled_orders(orders, open_trade)
                        open_trade.calculateRisk()  # Calculate risk only once when needed
                        del open_trades_by_symbol_direction[(norm_symbol, open_trade.direction)]

                        # Create a new trade for extra quantity
                        new_trade = open_next_coinbase_order_optimized(extra_quantity, order, username)
                        trades_to_save.append(new_trade)

                        # Add to lookup dictionary for future orders
                        open_trades_by_symbol_direction[(norm_symbol, new_trade.direction)] = new_trade
            else:
                open_trade.unfilled_orders.append(order)
                open_trade.sortOrders()
                open_trade.calculateRisk()
        elif order.filled:
            new_trade = Trade.fromOrder(order=order,
                                        username=username,
                                        building_reversed=first_import,
                                        direction=None)
            new_trade.update_trade_details()
            trades_to_save.append(new_trade)
            open_trades_by_symbol_direction[(norm_symbol, new_trade.direction)] = new_trade
        elif not order.filled:
            # Case 4: Standalone unfilled order (limit orders, etc.) - save directly to CoinbaseOrder table
            # This ensures get_coinbase_last_order_time() can find the oldest open order for proper date range queries
            cursor = trades_db.get_db_cursor()
            TradesDB.saveCoinbaseOrder(cursor, order.coinbaseOrder)
            trades_db.get_db_connection().commit()
            print(f"💾 Saved standalone unfilled order: {order.coinbaseOrder.coinbase_order_id} ({order.symbol})")

    # Check for expired open coinbase positions
    for trade in trades_to_save:
        if trade.status == TradeStatus.OPEN and trade.exchange == Exchange.COINBASE:
            # Check if the trade is a futures contract
            if trade.trade_orders[-1].coinbaseOrder.product_type != "FUTURE":
                continue
            symbol = trade.symbol  # SHB-30MAY25-CDE

            # Parse expiration date from symbol format: SYMBOL-DDMMMYY-CDE
            try:
                parts = symbol.split('-')
                if len(parts) >= 2:
                    date_part = parts[1]  # e.g., "30MAY25"
                    # Parse date: DD + MMM + YY format
                    expiration_date = datetime.strptime(date_part, "%d%b%y").replace(tzinfo=timezone.utc)

                    if datetime.now(timezone.utc) > expiration_date:
                        trade.close_expired_coinbase_trade(expiration_date)
            except (ValueError, IndexError) as e:
                # If parsing fails, fall back to the original 30-day check
                print(f"❌ Error parsing expiration date for {symbol}: {str(e)}", file=sys.stderr)
                traceback.print_exc()
                if trade.created_date < datetime.now(timezone.utc) - timedelta(days=30):
                    expiration_date = trade.created_date + timedelta(days=30)
                    trade.close_expired_coinbase_trade(expiration_date)

    calc_profit_and_save_trades(trades_to_save, trades_to_update)

    status["status"] = f"Done! Processed {count} Trades"
    return count


def process_orders(username, orders, status, current_open_positions, first_import):
    # only process orders where symbol is "BTC"
    # orders = [order for order in orders if normalize_symbol(order.symbol) == "SHB-30MAY25-CDE"]
    trades_to_save = []
    trades_to_update = []
    # Create dict of open symbol/direction to open qty
    open_positions = {}
    # Create a lookup dictionary for faster trade matching
    open_trades_by_symbol_direction = {}

    # Prime the dictionary with current open positions
    for trade in current_open_positions:
        if trade.status == TradeStatus.OPEN:
            norm_symbol = normalize_symbol(trade.symbol)
            open_trades_by_symbol_direction[(norm_symbol, trade.direction)] = trade
    # Create a copy of current_open_positions
    current_open_positions_original = current_open_positions.copy()

    for trade in current_open_positions:
        if trade.status == TradeStatus.OPEN:
            open_positions[normalize_symbol(trade.symbol) + trade.direction.value] = trade.openQty

    # Pre-normalize all order symbols to avoid repeated normalization
    normalized_order_symbols = {order: normalize_symbol(order.symbol) for order in orders}

    count = 0
    for count, order in enumerate(orders, start=1):
        status["status"] = f"Processing Trade #{count}"
        order.printInfo()

        # Fast lookup using pre-computed key
        norm_symbol = normalized_order_symbols[order]

        # Find matching open trade using dictionary lookup instead of linear search
        open_trade = None
        for direction in [TradeDirection.LONG, TradeDirection.SHORT]:
            key = (norm_symbol, direction)
            if key in open_trades_by_symbol_direction:
                open_trade = open_trades_by_symbol_direction[key]
                break

        if open_trade:
            trades_to_update.append(open_trade)
            if order.filled:
                # Extra quantity happens for Coinbase only when a user sells 3 with 1 open (-2) which results an
                live_position = open_positions.get(open_trade.symbol + open_trade.direction.value, False) > 0
                open_trade.update_with_order(order, live_position)

                if first_import:
                    # Check if open_trade quantity matches the open quantity in open_positions
                    if open_trade.openQty == open_positions.get(open_trade.symbol + open_trade.direction.value, None):
                        # Remove from open positions since its opening orders have been processed
                        key = (norm_symbol, open_trade.direction)
                        if open_trades_by_symbol_direction[key] == open_trade:
                            del open_positions[open_trade.symbol + open_trade.direction.value]
                            del open_trades_by_symbol_direction[key]
                            matching_position = next(
                                (pos for pos in current_open_positions if normalize_symbol(pos.symbol) == norm_symbol),
                                None
                            )
                            current_open_positions.remove(matching_position)
                            open_trade.calculate_profit()
                            if open_trade not in trades_to_save:  # Trade created from open position during first import
                                trades_to_save.append(open_trade)
                    else:
                        if open_trade not in trades_to_save:  # Trade created from open position during first import
                            trades_to_save.append(open_trade)
                            open_trades_by_symbol_direction[(norm_symbol, open_trade.direction)] = open_trade

                if open_trade.status == TradeStatus.CLOSED:
                    # Unfilled orders placed outside the trade fill's timeframe need to be accounted for
                    add_unfilled_orders(orders, open_trade)
                    open_trade.calculateRisk()  # Calculate risk only once when needed
                    del open_trades_by_symbol_direction[(norm_symbol, open_trade.direction)]
            else:
                open_trade.unfilled_orders.append(order)
                open_trade.sortOrders()
                open_trade.calculateRisk()
                if open_trade not in trades_to_save:  # Trade created from open position during first import
                    trades_to_save.append(open_trade)
                    open_trades_by_symbol_direction[(norm_symbol, open_trade.direction)] = open_trade
        # reduce orders cannot open trades, we are probably missing the previous leg of this trade
        elif order.filled and (first_import or not order.reduce):
            new_trade = Trade.fromOrder(order=order,
                                        username=username,
                                        building_reversed=first_import,
                                        direction=None)
            new_trade.update_trade_details()
            trades_to_save.append(new_trade)
            open_trades_by_symbol_direction[(norm_symbol, new_trade.direction)] = new_trade
        elif order.filled:
            print(f"Reduce Filled Order {order.order_id} does not match any open trade")
        elif not order.filled:
            # Case 4: Standalone unfilled order (limit orders, etc.) - save directly to appropriate order table
            # This ensures get_*_last_order_time() can find the oldest open order for proper date range queries
            cursor = trades_db.get_db_cursor()
            if order.bybitOrder:
                TradesDB.saveBybitOrder(cursor, order.bybitOrder)
                trades_db.get_db_connection().commit()
                print(f"💾 Saved standalone unfilled Bybit order: {order.bybitOrder.bybit_order_id} ({order.symbol})")
            elif order.sierraActivity:
                # For Sierra, we need to save the order first to get the order_id, then save the activity
                TradesDB.saveOrder(cursor, order)
                trades_db.get_db_connection().commit()
                print(f"💾 Saved standalone unfilled Sierra order: {order.sierraActivity.internalOrderId} ({order.symbol})")
            else:
                print(f"⚠️ Warning: Unfilled order {order.order_id} has no bybitOrder or sierraActivity to save")


        # if order.order_id == "4509f8cb-887b-4d00-a397-7c95b5010df4":
        #     break

    # Close any open trades during first import, except those in current_open_positions
    # These are trades cut in half from the 2 year API limit
    if first_import:
        # Create a set of trade IDs from current_open_positions for faster lookup
        current_open_position_ids = {trade.exchange_trade_id for trade in current_open_positions_original if
                                     trade.exchange_trade_id is not None}

        for trade in trades_to_save:
            if trade.status == TradeStatus.OPEN and trade.trade_orders[-1].order_id not in current_open_position_ids:
                trade.close_imported_trade()

    calc_profit_and_save_trades(trades_to_save, trades_to_update)

    status["status"] = f"Done! Processed {count} Trades"
    return count


def calc_profit_and_save_trades(trades_to_save, trades_to_update):
    # Check for any open trades and call get profit on them
    # Do this here to reduce calls to coinbase during import
    # remove duplicates from trades_to_update
    trades_to_update = list(set(trades_to_update))
    for trade in trades_to_save:
        if trade.status == TradeStatus.OPEN:
            # Only calculate profit for trades opened within the last 60 days
            if trade.created_date > datetime.now(timezone.utc) - timedelta(days=60):
                print(f"Calculating profit for OPEN trade: {trade.symbol}")
                trade.calculate_profit()
    # Batch database operations
    if trades_to_save:
        batch_save_trades(trades_to_save)
    # Remove duplicates from trades_to_update
    trades_to_update = [trade for trade in trades_to_update if trade not in trades_to_save]
    if trades_to_update:
        batch_update_trades(trades_to_update)


def open_next_coinbase_order_optimized(extra_quantity, order, username):
    """Optimized version that just returns the trade without saving to DB"""
    # Clone Order with remaining quantity, open new trade with that order
    extra_cbo_order = CoinbaseOrder.from_existing(order.coinbaseOrder, extra_quantity)
    extra_order = Order.fromCoinbaseOrder(extra_cbo_order)
    return Trade.fromOrder(order=extra_order, username=username)


def add_unfilled_orders(orders, open_trade):
    """
    Add unfilled orders to a trade.

    Args:
        orders: List of Order objects to check for unfilled orders
        open_trade: The Trade object to add unfilled orders to
    """
    # Process for both open and closed trades
    if open_trade.trade_orders and any(order.filled for order in open_trade.trade_orders):
        # Find the time range for the trade's filled orders
        filled_orders = [order for order in open_trade.trade_orders if order.filled]

        first_filled_order_created_time = min(
            filled_orders,
            key=lambda order: order.created_date,
            default=None
        ).created_date

        last_filled_order_created_time = max(
            filled_orders,
            key=lambda order: order.created_date,
            default=None
        ).created_date

        # Ensure we have first_filled_order_created_time and last_filled_order_created_time
        if not first_filled_order_created_time or not last_filled_order_created_time:
            return

        # Find all unfilled orders that were created during the trade's lifetime
        new_unfilled_orders = []

        for order in orders:
            # Skip filled orders
            if order.filled:
                continue

            # Skip orders for different symbols
            if normalize_symbol(order.symbol) != normalize_symbol(open_trade.symbol):
                continue

            # Check if the order was created during the trade's lifetime
            if not (first_filled_order_created_time < order.created_date < last_filled_order_created_time):
                continue

            # Avoid duplicates by checking if the order is already in unfilled_orders
            if any(uo.order_id == order.order_id for uo in open_trade.unfilled_orders):
                continue

            # Add the order to the trade
            new_unfilled_orders.append(order)

        # Add the new unfilled orders to the existing ones
        open_trade.unfilled_orders.extend(new_unfilled_orders)

        # Re-Sort orders
        open_trade.sortOrders()


def normalize_symbol(symbol):
    if symbol.endswith("-USDT"):
        return symbol.replace("-USDT", "-USD")
    elif symbol.endswith("-USDC"):
        return symbol.replace("-USDC", "-USD")
    return symbol


def batch_save_or_update_trades(trades, operation="save"):
    """Save or update multiple trades in a single transaction"""
    connection = trades_db.get_db_connection()
    cursor = connection.cursor()

    try:
        # Begin transaction
        connection.execute("BEGIN TRANSACTION")
        for trade in trades:
            if operation == "save":
                TradesDB.saveTrade(trade, cursor)
            else:  # update
                TradesDB.updateTrade(trade, cursor)
        # Commit transaction
        connection.execute("COMMIT")
    except Exception as e:
        # Rollback on error
        connection.execute("ROLLBACK")
        print(f"Error in batch_{operation}_trades: {e}")
        raise


def batch_save_trades(trades):
    """Save multiple trades in a single transaction"""
    return batch_save_or_update_trades(trades, "save")


def batch_update_trades(trades):
    """Update multiple trades in a single transaction"""
    return batch_save_or_update_trades(trades, "update")

# Program Start #

# result = Bybit.getExecutions()


# SIERRA CONTROLS #

# df = Helper.get_sierra_trades_file()
# processSierraActivities(df)

########################

# COINBASE CONTROLS #

# cos = Coinbase.get_futures_positions()
# Coinbase.get_all_conversions()

# if not TradesDB.check_db_exists():
#     TradesDB.createTradeDB()

# xlm_account_id = CoinbaseWallet.get_xlm_account_id()
# print(f"Found XLM Account ID: {xlm_account_id}")
#
# transactions = CoinbaseWallet.get_transactions(xlm_account_id)

########################
